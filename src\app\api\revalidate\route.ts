import { NextRequest, NextResponse } from 'next/server';
import { revalidatePath, revalidateTag } from 'next/cache';

// Secret token for security (should be set in environment variables)
const REVALIDATE_SECRET = process.env.REVALIDATE_SECRET || 'your-secret-token';

export async function POST(request: NextRequest) {
  try {
    // Check for secret token
    const authHeader = request.headers.get('authorization');
    const token = authHeader?.replace('Bearer ', '');
    
    if (token !== REVALIDATE_SECRET) {
      return NextResponse.json(
        { error: 'Invalid token' },
        { status: 401 }
      );
    }

    const body = await request.json();
    const { type, path, tag, productSlug, categorySlug, mainCategorySlug } = body;

    console.log('Revalidation request:', { type, path, tag, productSlug, categorySlug, mainCategorySlug });

    switch (type) {
      case 'product':
        if (productSlug) {
          // Revalidate specific product page using new URL structure
          await revalidatePath(`/product/${productSlug}`);
          // Also revalidate related category pages if provided
          if (categorySlug && mainCategorySlug) {
            await revalidatePath(`/shop/${mainCategorySlug}/${categorySlug}`);
            await revalidatePath(`/shop/${mainCategorySlug}`);
          }
          // And the main shop page
          await revalidatePath('/shop');

          console.log(`Revalidated product: ${productSlug} and related pages`);
        }
        break;

      case 'category':
        if (categorySlug && mainCategorySlug) {
          // Revalidate specific category page
          await revalidatePath(`/shop/${mainCategorySlug}/${categorySlug}`);
          // Also revalidate the main category page
          await revalidatePath(`/shop/${mainCategorySlug}`);
          // And the main shop page
          await revalidatePath('/shop');
          
          console.log(`Revalidated category: ${categorySlug} and related pages`);
        }
        break;

      case 'main-category':
        if (mainCategorySlug) {
          // Revalidate specific main category page
          await revalidatePath(`/shop/${mainCategorySlug}`);
          // And the main shop page
          await revalidatePath('/shop');
          
          console.log(`Revalidated main category: ${mainCategorySlug} and related pages`);
        }
        break;

      case 'shop':
        // Revalidate entire shop section
        await revalidatePath('/shop');
        console.log('Revalidated main shop page');
        break;

      case 'path':
        if (path) {
          await revalidatePath(path);
          console.log(`Revalidated path: ${path}`);
        }
        break;

      case 'tag':
        if (tag) {
          await revalidateTag(tag);
          console.log(`Revalidated tag: ${tag}`);
        }
        break;

      case 'all':
        // Revalidate all shop-related pages
        await revalidatePath('/shop');
        await revalidatePath('/');
        console.log('Revalidated all main pages');
        break;

      default:
        return NextResponse.json(
          { error: 'Invalid revalidation type' },
          { status: 400 }
        );
    }

    return NextResponse.json({
      success: true,
      message: 'Revalidation completed',
      timestamp: new Date().toISOString(),
    });

  } catch (error) {
    console.error('Revalidation error:', error);
    return NextResponse.json(
      { error: 'Revalidation failed', details: error instanceof Error ? error.message : 'Unknown error' },
      { status: 500 }
    );
  }
}

// Example usage:
// POST /api/revalidate
// Headers: { "Authorization": "Bearer your-secret-token" }
// Body: {
//   "type": "product",
//   "productSlug": "gentle-foaming-cleanser",
//   "categorySlug": "face-cleansers",  // optional - for revalidating related category pages
//   "mainCategorySlug": "face-care"    // optional - for revalidating related category pages
// }

// Or for a category:
// Body: { 
//   "type": "category", 
//   "categorySlug": "face-cleansers", 
//   "mainCategorySlug": "face-care" 
// }

// Or for a specific path:
// Body: { "type": "path", "path": "/shop/face-care" }

// Or for a tag:
// Body: { "type": "tag", "tag": "products" }
