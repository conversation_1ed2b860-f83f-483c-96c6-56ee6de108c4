import { NextRequest, NextResponse } from 'next/server';

// Force dynamic rendering
export const dynamic = 'force-dynamic';

// Get the API base URL and path prefix from environment variables
const API_BASE_URL = process.env.NEXT_PUBLIC_API_BASE_URL || 'http://localhost:3300';
const API_SHOP_PATH_PREFIX = process.env.NEXT_PUBLIC_API_SHOP_PATH_PREFIX || '/api/shop';

// Log the API configuration to help with debugging
console.log('Shop Products API - Base URL:', API_BASE_URL);
console.log('Shop Products API - Shop Path Prefix:', API_SHOP_PATH_PREFIX);

// GET handler for fetching all products
export async function GET(request: NextRequest) {
  try {
    const url = new URL(request.url);
    const searchParams = url.searchParams;

    // Get query parameters
    const categoryId = searchParams.get('categoryId');
    const page = searchParams.get('page') || '1';
    const limit = searchParams.get('limit') || '20';

    // Construct the backend URL based on parameters
    let backendUrl = `${API_BASE_URL}${API_SHOP_PATH_PREFIX}/products`;

    // Add query parameters if they exist
    const queryParams = new URLSearchParams();
    if (categoryId) queryParams.append('categoryId', categoryId);
    queryParams.append('page', page);
    queryParams.append('limit', limit);

    // Append query parameters to the URL
    if (queryParams.toString()) {
      backendUrl += `?${queryParams.toString()}`;
    }

    console.log(`API proxy - Calling backend URL: ${backendUrl}`);

    const response = await fetch(backendUrl, {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
        'Accept': 'application/json',
      },
      cache: 'no-store'
    });

    // Check if the response is JSON
    const contentType = response.headers.get('content-type');
    if (!contentType || !contentType.includes('application/json')) {
      console.error('Invalid response type:', contentType);
      const responseText = await response.text();
      console.error('Response preview:', responseText.substring(0, 200));

      return NextResponse.json(
        {
          error: 'Backend server returned invalid response format',
          details: 'Expected JSON but received ' + (contentType || 'unknown content type')
        },
        { status: 502 }
      );
    }

    if (!response.ok) {
      const errorData = await response.json().catch(() => ({}));
      console.error('Backend API error:', {
        status: response.status,
        statusText: response.statusText,
        error: errorData
      });
      return NextResponse.json(
        { error: 'Failed to fetch products', details: errorData },
        { status: response.status }
      );
    }

    const data = await response.json();
    return NextResponse.json(data);
  } catch (error) {
    console.error('API proxy error (GET /shop/products):', error);
    return NextResponse.json(
      { 
        error: 'Failed to fetch products',
        message: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    );
  }
}
