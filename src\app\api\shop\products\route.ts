import { NextRequest, NextResponse } from 'next/server';

// Get the API base URL and path prefix from environment variables
const API_BASE_URL = process.env.NEXT_PUBLIC_API_BASE_URL || 'http://localhost:3300';
const API_SHOP_PATH_PREFIX = process.env.NEXT_PUBLIC_API_SHOP_PATH_PREFIX || '/api/shop';

// Log the API configuration to help with debugging
console.log('Shop Products API - Base URL:', API_BASE_URL);
console.log('Shop Products API - Shop Path Prefix:', API_SHOP_PATH_PREFIX);

// GET handler for fetching all products
export async function GET(request: NextRequest) {
  try {
    const url = new URL(request.url);
    const searchParams = url.searchParams;

    // Get query parameters
    const categoryId = searchParams.get('categoryId');
    const page = searchParams.get('page') || '1';
    const limit = searchParams.get('limit') || '20';

    // Construct the backend URL based on parameters
    // Use the shop API path prefix from environment variables
    let backendUrl = `${API_BASE_URL}${API_SHOP_PATH_PREFIX}/products`;

    // Add query parameters if they exist
    const queryParams = new URLSearchParams();
    if (categoryId) queryParams.append('categoryId', categoryId);
    queryParams.append('page', page);
    queryParams.append('limit', limit);

    // Append query parameters to the URL
    if (queryParams.toString()) {
      backendUrl += `?${queryParams.toString()}`;
    }

    console.log(`API proxy - Calling backend URL: ${backendUrl}`);

    const response = await fetch(backendUrl, {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
      },
      // Add cache: 'no-store' to prevent caching issues
      cache: 'no-store'
    });

    if (!response.ok) {
      return NextResponse.json(
        { error: 'Failed to fetch products' },
        { status: response.status }
      );
    }

    const data = await response.json();
    return NextResponse.json(data);
  } catch (error) {
    console.error('API proxy error (GET /shop/products):', error);
    return NextResponse.json(
      { error: 'Failed to fetch products' },
      { status: 500 }
    );
  }
}
