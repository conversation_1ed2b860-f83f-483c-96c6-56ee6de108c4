import { NextRequest, NextResponse } from 'next/server';

// Get the API base URL and path prefix from environment variables
const API_BASE_URL = process.env.NEXT_PUBLIC_API_BASE_URL || 'http://localhost:3300';
const API_SHOP_PATH_PREFIX = process.env.NEXT_PUBLIC_API_SHOP_PATH_PREFIX || '/api/shop';

// Log the API configuration to help with debugging
console.log('Product by Slug API - Base URL:', API_BASE_URL);
console.log('Product by Slug API - Shop Path Prefix:', API_SHOP_PATH_PREFIX);

// GET handler for fetching a product by slug
export async function GET(
  request: NextRequest,
  { params }: { params: { slug: string } }
) {
  try {
    const slug = params.slug;
    const url = new URL(request.url);
    const password = url.searchParams.get('password') || '';

    console.log(`API proxy - Fetching product with slug ${slug}`);
    console.log(`API proxy - Request URL: ${request.url}`);

    // Construct the URL with password if provided
    // The backend expects slugs at /api/shop/products/slug/[slug]
    let backendUrl = `${API_BASE_URL}${API_SHOP_PATH_PREFIX}/products/slug/${slug}`;
    if (password) {
      backendUrl += `?password=${encodeURIComponent(password)}`;
    }
    
    console.log(`API proxy - Shop backend URL: ${backendUrl}`);

    const response = await fetch(backendUrl, {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
      },
      cache: 'no-store'
    });

    if (!response.ok) {
      const errorStatus = response.status;
      let errorMessage = `Product with slug ${slug} not found`;
      
      try {
        const errorData = await response.json();
        errorMessage = errorData.message || errorMessage;
      } catch (e) {
        // If we can't parse the error response, use the default message
      }
      
      return NextResponse.json(
        { message: errorMessage, error: response.statusText, statusCode: errorStatus },
        { status: errorStatus }
      );
    }

    const data = await response.json();
    return NextResponse.json(data);
  } catch (error) {
    console.error(`API proxy error (GET /shop/products/slug/${params.slug}):`, error);
    return NextResponse.json(
      { message: `Failed to fetch product with slug ${params.slug}`, error: 'Internal Server Error', statusCode: 500 },
      { status: 500 }
    );
  }
}
