import { NextRequest, NextResponse } from 'next/server';

// Get the API base URL and path prefix from environment variables
const API_BASE_URL = process.env.NEXT_PUBLIC_API_BASE_URL || 'http://localhost:3300';
const API_STORE_PATH_PREFIX = process.env.NEXT_PUBLIC_API_STORE_PATH_PREFIX || '/api/store';

// Log the API configuration to help with debugging
console.log('Product by ID API - Base URL:', API_BASE_URL);
console.log('Product by ID API - Store Path Prefix:', API_STORE_PATH_PREFIX);

// GET handler for fetching a product by ID
export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const id = params.id;

    const response = await fetch(`${API_BASE_URL}${API_STORE_PATH_PREFIX}/products/${id}`, {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
      },
    });

    if (!response.ok) {
      return NextResponse.json(
        { error: `Product with ID ${id} not found` },
        { status: response.status }
      );
    }

    const data = await response.json();
    return NextResponse.json(data);
  } catch (error) {
    console.error(`API proxy error (GET /products/${params.id}):`, error);
    return NextResponse.json(
      { error: `Failed to fetch product with ID ${params.id}` },
      { status: 500 }
    );
  }
}

// PATCH handler for updating a product
export async function PATCH(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const id = params.id;
    const body = await request.json();

    console.log(`API proxy - Updating product with ID ${id}:`, body);

    // Always remove the ProductAttribute field for the simple product endpoints
    // Grouped products should use their dedicated endpoints
    if ('ProductAttribute' in body) {
      delete body.ProductAttribute;
      console.log(`API proxy - Removed ProductAttribute field for product update`);
    }

    // Also remove variants field if present
    if ('variants' in body) {
      delete body.variants;
      console.log(`API proxy - Removed variants field for product update`);
    }

    const backendUrl = `${API_BASE_URL}${API_STORE_PATH_PREFIX}/products/${id}`;
    console.log(`API proxy - Calling backend URL: ${backendUrl}`);
    console.log(`API proxy - Cleaned request body:`, body);

    const response = await fetch(backendUrl, {
      method: 'PATCH',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(body),
    });

    console.log(`API proxy - Response status for updating product ${id}:`, response.status);

    if (!response.ok) {
      const errorText = await response.text();
      console.error(`API proxy - Error response from backend: ${errorText}`);
      return NextResponse.json(
        { error: `Failed to update product with ID ${id}` },
        { status: response.status }
      );
    }

    let data;
    try {
      data = await response.json();
      console.log(`API proxy - Updated product data from backend:`, data);
    } catch (e) {
      console.error(`API proxy - Error parsing JSON response for product ${id}:`, e);
      return NextResponse.json(
        { error: `Failed to parse response for product with ID ${id}` },
        { status: 500 }
      );
    }

    return NextResponse.json(data);
  } catch (error) {
    console.error(`API proxy error (PATCH /products/${params.id}):`, error);
    return NextResponse.json(
      { error: `Failed to update product with ID ${params.id}` },
      { status: 500 }
    );
  }
}

// DELETE handler for deleting a product
export async function DELETE(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const id = params.id;

    const response = await fetch(`${API_BASE_URL}${API_STORE_PATH_PREFIX}/products/${id}`, {
      method: 'DELETE',
      headers: {
        'Content-Type': 'application/json',
      },
    });

    if (!response.ok) {
      return NextResponse.json(
        { error: `Failed to delete product with ID ${id}` },
        { status: response.status }
      );
    }

    const data = await response.json();
    return NextResponse.json(data);
  } catch (error) {
    console.error(`API proxy error (DELETE /products/${params.id}):`, error);
    return NextResponse.json(
      { error: `Failed to delete product with ID ${params.id}` },
      { status: 500 }
    );
  }
}
