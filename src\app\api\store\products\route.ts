import { NextRequest, NextResponse } from 'next/server';

// Get the API base URL and path prefix from environment variables
const API_BASE_URL = process.env.NEXT_PUBLIC_API_BASE_URL || 'http://localhost:3300';
const API_STORE_PATH_PREFIX = process.env.NEXT_PUBLIC_API_STORE_PATH_PREFIX || '/api/store';

// Log the API configuration to help with debugging
console.log('Products API - Base URL:', API_BASE_URL);
console.log('Products API - Store Path Prefix:', API_STORE_PATH_PREFIX);

// GET handler for fetching all products
export async function GET(request: NextRequest) {
  try {
    const response = await fetch(`${API_BASE_URL}${API_STORE_PATH_PREFIX}/products`, {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
      },
    });

    const data = await response.json();
    return NextResponse.json(data);
  } catch (error) {
    console.error('API proxy error (GET /products):', error);
    return NextResponse.json(
      { error: 'Failed to fetch products' },
      { status: 500 }
    );
  }
}

// POST handler for creating a new product
export async function POST(request: NextRequest) {
  try {
    const body = await request.json();

    console.log(`API proxy - Creating new product:`, body);

    // Always remove the ProductAttribute field for the simple product endpoints
    // Grouped products should use their dedicated endpoints
    if ('ProductAttribute' in body) {
      delete body.ProductAttribute;
      console.log(`API proxy - Removed ProductAttribute field for product creation`);
    }

    // Also remove variants field if present
    if ('variants' in body) {
      delete body.variants;
      console.log(`API proxy - Removed variants field for product creation`);
    }

    console.log(`API proxy - Cleaned request body:`, body);

    const response = await fetch(`${API_BASE_URL}${API_STORE_PATH_PREFIX}/products`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(body),
    });

    const data = await response.json();
    return NextResponse.json(data, { status: response.status });
  } catch (error) {
    console.error('API proxy error (POST /products):', error);
    return NextResponse.json(
      { error: 'Failed to create product' },
      { status: 500 }
    );
  }
}
