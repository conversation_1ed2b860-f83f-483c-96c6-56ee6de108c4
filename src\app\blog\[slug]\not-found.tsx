import Link from 'next/link';
import { Fi<PERSON>rrowLeft, FiHome, FiSearch } from 'react-icons/fi';

export default function BlogPostNotFound() {
  return (
    <div className="min-h-screen bg-gray-50 flex items-center justify-center px-4">
      <div className="max-w-md w-full text-center">
        {/* 404 Illustration */}
        <div className="mb-8">
          <div className="text-8xl mb-4">📝</div>
          <h1 className="text-4xl font-bold text-gray-900 mb-2">404</h1>
          <h2 className="text-xl font-semibold text-gray-700 mb-4">Blog Post Not Found</h2>
          <p className="text-gray-600 leading-relaxed">
            Sorry, we couldn't find the blog post you're looking for. 
            It might have been moved, deleted, or the URL might be incorrect.
          </p>
        </div>

        {/* Action Buttons */}
        <div className="space-y-4">
          <Link
            href="/blog"
            className="inline-flex items-center gap-2 bg-main-color text-white px-6 py-3 rounded-lg hover:bg-main-color/90 transition-colors font-medium"
          >
            <FiArrowLeft className="w-4 h-4" />
            Back to Blog
          </Link>
          
          <div className="flex gap-3 justify-center">
            <Link
              href="/"
              className="inline-flex items-center gap-2 text-gray-600 hover:text-main-color transition-colors"
            >
              <FiHome className="w-4 h-4" />
              Homepage
            </Link>
            
            <Link
              href="/blog"
              className="inline-flex items-center gap-2 text-gray-600 hover:text-main-color transition-colors"
            >
              <FiSearch className="w-4 h-4" />
              Search Articles
            </Link>
          </div>
        </div>

        {/* Suggestions */}
        <div className="mt-12 p-6 bg-white rounded-lg shadow-sm border">
          <h3 className="font-semibold text-gray-900 mb-3">What you can do:</h3>
          <ul className="text-sm text-gray-600 space-y-2 text-left">
            <li>• Check the URL for any typos</li>
            <li>• Browse our latest blog posts</li>
            <li>• Use the search function to find specific topics</li>
            <li>• Visit our homepage for featured content</li>
          </ul>
        </div>
      </div>
    </div>
  );
}
