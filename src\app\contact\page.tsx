import type { Metadata } from 'next';
import ContactUsContent from '@/components/contact/ContactUsContent';

export const metadata: Metadata = {
  title: 'Contact Us - Get in Touch with COCOJOJO',
  description: 'Contact COCOJOJO for inquiries about our natural skincare products. Call us at (+1) ************ <NAME_EMAIL>. Visit our store in Santa Ana, CA.',
  keywords: 'contact cocojojo, customer service, natural skincare support, santa ana store, cocojojo phone number',
  openGraph: {
    title: 'Contact COCOJOJO - Natural Beauty Support',
    description: 'Get in touch with COCOJOJO for product inquiries, customer support, and business opportunities.',
    type: 'website',
    url: '/contact',
  },
  twitter: {
    card: 'summary_large_image',
    title: 'Contact COCOJOJO - Natural Beauty Support',
    description: 'Get in touch with COCOJOJO for product inquiries, customer support, and business opportunities.',
  },
};

export default function ContactPage() {
  return (
    <div className="min-h-screen bg-gray-50">
      <ContactUsContent />
    </div>
  );
}
