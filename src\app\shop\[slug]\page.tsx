"use client";

import { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { FiShoppingBag, FiStar, FiInfo, FiList, FiLock, FiEyeOff } from 'react-icons/fi';
import ProductImageGallery from '@/components/shop/product/ProductImageGallery';
import LoadingSpinner from '@/components/shop/product/LoadingSpinner';
import ProductNotFound from '@/components/shop/product/ProductNotFound';
import ProductVariantSelector from '@/components/shop/product/ProductVariantSelector';
import ProductListings from '@/components/shop/product/ProductListings';
import ProductReviews from '@/components/shop/product/ProductReviews';
import { useCartStore } from '@/hooks/useCartStore';

// Define types based on the API response
interface ProductImage {
  id: number;
  url: string;
  position: number;
}

interface ProductCategory {
  id: number;
  name: string;
  slug: string;
  imageUrl?: string;
}

interface ProductTag {
  id: number;
  name: string;
  slug: string;
}

interface ProductListing {
  id: number;
  title: string;
  content: string;
}

interface AttributeValue {
  id: number;
  value: string;
}

interface Attribute {
  id: number;
  name: string;
  values: AttributeValue[];
}

interface ProductAttribute {
  attribute: Attribute;
  values: AttributeValue[];
}

interface VariantAttribute {
  value: AttributeValue;
}

interface ProductVariant {
  id: number;
  sku: string;
  price: number;
  stockQuantity: number;
  stockStatus: string;
  ProductImage: ProductImage[];
  attributes: VariantAttribute[];
}

interface Product {
  id: number;
  sku: string;
  name: string;
  slug: string;
  description: string;
  shortDescription: string;
  price: number;
  salePrice: number | null;
  stockQuantity: number;
  stockStatus: string;
  type: string;
  access: string;
  createdAt: string;
  updatedAt: string;
  images: ProductImage[];
  categories: ProductCategory[];
  tags: ProductTag[];
  listings: ProductListing[];
  ProductAttribute?: ProductAttribute[];
  variants?: ProductVariant[];
}

const ProductPage = ({ params }: { params: { slug: string } }) => {
  const [product, setProduct] = useState<Product | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [selectedVariant, setSelectedVariant] = useState<ProductVariant | null>(null);
  const [selectedAttributes, setSelectedAttributes] = useState<Record<string, AttributeValue>>({});
  const [isAddingToCart, setIsAddingToCart] = useState(false);
  const [quantity, setQuantity] = useState(1);
  const router = useRouter();

  useEffect(() => {
    const fetchProduct = async () => {
      try {
        // Special handling for '404' slug to prevent infinite loops
        if (params.slug === '404') {
          console.error('Detected 404 slug, showing error to prevent infinite loop');
          setError('Product not found');
          setLoading(false);
          return;
        }

        setLoading(true);
        console.log(`Fetching product with slug: ${params.slug}`);

        // Use the API endpoint to fetch product by slug
        // The backend expects slugs at /api/shop/products/slug/[slug]
        const apiUrl = `/api/shop/products/slug/${params.slug}`;
        console.log(`Fetching from API URL: ${apiUrl}`);

        const response = await fetch(apiUrl, {
          // Add cache: 'no-store' to prevent caching and infinite loops
          cache: 'no-store'
        });

        if (!response.ok) {
          // Don't redirect to /404 as it causes an infinite loop
          // Instead, set the error state and let the component handle it
          console.error(`Product not found: ${params.slug}`);
          setError(`Product not found: ${params.slug}`);
          setLoading(false);
          return;
        }

        const data = await response.json();
        console.log('Product data received:', data);

        // Check if the data is valid
        if (!data || !data.id) {
          console.error('Invalid product data received:', data);
          setError('Invalid product data received');
          setLoading(false);
          return;
        }

        // Log all the fields to help debug
        console.log('Product ID:', data.id);
        console.log('Product Name:', data.name);
        console.log('Product Slug:', data.slug);
        console.log('Product Type:', data.type);
        console.log('Product Images:', data.images);



        setProduct(data);

        // If it's a grouped product, initialize the selected variant
        if (data.type === 'GROUPED' && data.variants && data.variants.length > 0) {
          console.log('Initializing grouped product with variants:', data.variants);

          // Find a variant with images if possible
          const variantWithImages = data.variants.find((v: ProductVariant) => v.ProductImage && v.ProductImage.length > 0);
          const initialVariant = variantWithImages || data.variants[0];

          console.log('Selected initial variant:', initialVariant);
          console.log('Initial variant images:', initialVariant.ProductImage);

          setSelectedVariant(initialVariant);

          // Initialize selected attributes based on the selected variant
          if (initialVariant.attributes) {
            const initialAttributes: Record<string, AttributeValue> = {};
            initialVariant.attributes.forEach((attr: VariantAttribute) => {
              const attributeInfo = data.ProductAttribute?.find((pa: any) =>
                pa.values.some((v: any) => v.id === attr.value.id)
              );

              const attributeId = attributeInfo?.attribute.id;

              if (attributeId) {
                initialAttributes[attributeId.toString()] = attr.value;
                console.log(`Set initial attribute ${attributeId} to:`, attr.value);
              }
            });
            setSelectedAttributes(initialAttributes);
          }
        }
      } catch (err) {
        console.error('Error fetching product:', err);
        setError(err instanceof Error ? err.message : 'An error occurred');
      } finally {
        setLoading(false);
      }
    };

    fetchProduct();
  }, [params.slug, router]);

  // Handle attribute selection for grouped products
  const handleAttributeChange = (attributeId: number, value: AttributeValue) => {
    console.log(`Attribute changed: ID ${attributeId}, Value:`, value);

    const newSelectedAttributes = {
      ...selectedAttributes,
      [attributeId.toString()]: value
    };
    setSelectedAttributes(newSelectedAttributes);

    // Find the matching variant based on selected attributes
    if (product?.variants) {
      const matchingVariant = findMatchingVariant(product.variants, newSelectedAttributes);
      if (matchingVariant) {
        console.log('Found matching variant:', matchingVariant);
        console.log('Variant images:', matchingVariant.ProductImage);
        setSelectedVariant(matchingVariant);
      } else {
        console.log('No matching variant found for selected attributes:', newSelectedAttributes);
      }
    }
  };

  // Find a variant that matches the selected attributes
  const findMatchingVariant = (variants: ProductVariant[], selectedAttrs: Record<string, AttributeValue>) => {
    console.log('Finding matching variant among', variants.length, 'variants');
    console.log('Selected attributes:', selectedAttrs);

    // First try to find an exact match (all attributes match)
    const exactMatch = variants.find(variant => {
      // Check if all selected attributes match this variant
      const allAttributesMatch = Object.entries(selectedAttrs).every(([_, attrValue]) => {
        return variant.attributes.some(va => va.value.id === attrValue.id);
      });

      // Check if variant has the same number of attributes as selected
      const sameAttributeCount = variant.attributes.length === Object.keys(selectedAttrs).length;

      return allAttributesMatch && sameAttributeCount;
    });

    if (exactMatch) {
      console.log('Found exact match variant:', exactMatch);
      return exactMatch;
    }

    // If no exact match, find the best partial match
    console.log('No exact match found, looking for partial match');
    return variants.find(variant => {
      // Check if all selected attributes match this variant
      return Object.entries(selectedAttrs).every(([_, attrValue]) => {
        return variant.attributes.some(va => va.value.id === attrValue.id);
      });
    });
  };

  // Determine if the product is new (created within the last week)
  const isNewProduct = (createdAt: string) => {
    const createdDate = new Date(createdAt);
    const now = new Date();
    const oneWeekAgo = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000);
    return createdDate > oneWeekAgo;
  };

  if (loading) {
    return <LoadingSpinner />;
  }

  if (error || !product) {
    return <ProductNotFound error={error || "Product not found"} slug={params.slug} />;
  }

  // Determine the current price (accounting for sale price and variants)
  const currentPrice = selectedVariant
    ? selectedVariant.price
    : product.salePrice !== null
      ? product.salePrice
      : product.price;

  // Determine the original price for comparison
  const originalPrice = selectedVariant
    ? (product.price > selectedVariant.price ? product.price : null)
    : product.salePrice !== null
      ? product.price
      : null;

  return (
    <div className="px-4 md:px-8 lg:px-16 xl:px-32 2xl:px-64 py-12">
      {/* Product Header - Mobile */}
      <div className="lg:hidden mb-8">
        <h1 className="text-3xl font-medium text-gray-800 mb-2">{product.name}</h1>
        <div className="flex items-center gap-4 mb-4">
          {/* Product badges */}
          <div className="flex gap-2">
            {isNewProduct(product.createdAt) && (
              <span className="bg-green-500 text-white text-xs font-medium px-2 py-1 rounded">New</span>
            )}
            {product.salePrice && (
              <span className="bg-main-color text-white text-xs font-medium px-2 py-1 rounded">Sale</span>
            )}
            {product.access === 'PROTECTED' && (
              <span className="bg-amber-500 text-white text-xs font-medium px-2 py-1 rounded flex items-center gap-1">
                <FiLock size={12} /> Protected
              </span>
            )}
            {product.access === 'PRIVATE' && (
              <span className="bg-red-500 text-white text-xs font-medium px-2 py-1 rounded flex items-center gap-1">
                <FiEyeOff size={12} /> Private
              </span>
            )}
          </div>

          {/* SKU */}
          <span className="text-sm text-gray-500">SKU: {selectedVariant ? selectedVariant.sku : product.sku}</span>
        </div>
      </div>

      <div className="flex flex-col lg:flex-row gap-12">
        {/* Product Images */}
        <div className="w-full lg:w-1/2">

          {/* Key prop forces re-render when variant changes */}
          <ProductImageGallery
            key={selectedVariant?.id || 'default'}
            images={
              selectedVariant?.ProductImage?.length
                ? selectedVariant.ProductImage
                : (product.images?.length ? product.images : [])
            }
          />
        </div>

        {/* Product Details */}
        <div className="w-full lg:w-1/2">
          {/* Product Header - Desktop */}
          <div className="hidden lg:block mb-8">
            <h1 className="text-4xl font-medium text-gray-800 mb-3">{product.name}</h1>
            <div className="flex items-center gap-4 mb-4">
              {/* Product badges */}
              <div className="flex gap-2">
                {isNewProduct(product.createdAt) && (
                  <span className="bg-green-500 text-white text-xs font-medium px-2 py-1 rounded">New</span>
                )}
                {product.salePrice && (
                  <span className="bg-main-color text-white text-xs font-medium px-2 py-1 rounded">Sale</span>
                )}
                {product.access === 'PROTECTED' && (
                  <span className="bg-amber-500 text-white text-xs font-medium px-2 py-1 rounded flex items-center gap-1">
                    <FiLock size={12} /> Protected
                  </span>
                )}
                {product.access === 'PRIVATE' && (
                  <span className="bg-red-500 text-white text-xs font-medium px-2 py-1 rounded flex items-center gap-1">
                    <FiEyeOff size={12} /> Private
                  </span>
                )}
              </div>

              {/* SKU */}
              <span className="text-sm text-gray-500">SKU: {selectedVariant ? selectedVariant.sku : product.sku}</span>
            </div>
          </div>

          {/* Short Description */}
          <div className="mb-6">
            <div
              className="text-gray-600 leading-relaxed"
              dangerouslySetInnerHTML={{ __html: product.shortDescription }}
            />
          </div>

          {/* Price */}
          <div className="mb-8">
            <div className="flex items-center gap-4">
              {originalPrice && (
                <span className="text-xl text-gray-500 line-through">${originalPrice.toFixed(2)}</span>
              )}
              <span className="text-3xl font-medium text-main-color">${currentPrice.toFixed(2)}</span>
            </div>
          </div>

          {/* Divider */}
          <div className="h-px bg-gray-200 my-8"></div>

          {/* Variant Selection for Grouped Products */}
          {product.type === 'GROUPED' && product.ProductAttribute && (
            <ProductVariantSelector
              productAttributes={product.ProductAttribute}
              selectedAttributes={selectedAttributes}
              onAttributeChange={handleAttributeChange}
            />
          )}

          {/* Stock Status */}
          <div className="mb-8">
            <div className="flex items-center gap-2">
              <span className="font-medium text-gray-800">Availability:</span>
              {(selectedVariant ? selectedVariant.stockStatus : product.stockStatus) === 'IN_STOCK' ? (
                <span className="text-green-600">In Stock</span>
              ) : (
                <span className="text-red-600">Out of Stock</span>
              )}
            </div>
            {/* Only show stock quantity if there are 1 or 2 items left */}
            {(selectedVariant?.stockQuantity || product.stockQuantity) > 0 &&
             (selectedVariant?.stockQuantity || product.stockQuantity) <= 2 && (
              <div className="text-sm text-red-600 mt-1 font-medium">
                Only {selectedVariant ? selectedVariant.stockQuantity : product.stockQuantity} left in stock!
              </div>
            )}
          </div>

          {/* Quantity Selector */}
          <div className="mb-8">
            <div className="flex items-center gap-2 mb-2">
              <span className="font-medium text-gray-800">Quantity:</span>
            </div>
            <div className="flex items-center border border-gray-300 rounded-md w-32">
              <button
                className="px-3 py-2 text-gray-600 hover:bg-gray-100 transition-colors"
                onClick={() => {
                  if (quantity > 1) {
                    setQuantity(quantity - 1);
                  }
                }}
              >
                -
              </button>
              <input
                type="number"
                className="w-full text-center border-0 focus:ring-0 focus:outline-none"
                value={quantity}
                min={1}
                max={
                  selectedVariant?.stockQuantity || product.stockQuantity
                    ? selectedVariant?.stockQuantity || product.stockQuantity
                    : 999
                }
                readOnly
              />
              <button
                className="px-3 py-2 text-gray-600 hover:bg-gray-100 transition-colors"
                onClick={() => {
                  const maxQuantity = selectedVariant?.stockQuantity || product.stockQuantity || 999;
                  if (quantity < maxQuantity) {
                    setQuantity(quantity + 1);
                  }
                }}
              >
                +
              </button>
            </div>
          </div>

          {/* Add to Cart Button */}
          <button
            className={`w-full py-3 px-6 rounded-lg flex items-center justify-center gap-2 text-white font-medium transition-colors ${
              (selectedVariant ? selectedVariant.stockStatus : product.stockStatus) === 'IN_STOCK'
                ? 'bg-main-color hover:bg-main-color/90'
                : 'bg-gray-400 cursor-not-allowed'
            }`}
            disabled={(selectedVariant ? selectedVariant.stockStatus : product.stockStatus) !== 'IN_STOCK' || isAddingToCart}
            onClick={() => {
              setIsAddingToCart(true);
              const { addItem } = useCartStore.getState();
              const variant = selectedVariant;
              const productToAdd = {
                id: variant ? variant.id : product.id,
                name: product.name,
                price: variant ? variant.price : (product.salePrice !== null ? product.salePrice : product.price),
                image: variant && variant.ProductImage && variant.ProductImage.length > 0
                  ? variant.ProductImage[0].url
                  : (product.images && product.images.length > 0 ? product.images[0].url : ''),
                sku: variant ? variant.sku : product.sku,
                stockQuantity: variant ? variant.stockQuantity : product.stockQuantity,
                stockStatus: variant ? variant.stockStatus : product.stockStatus,
                variantId: variant ? variant.id : null
              };
              addItem(productToAdd, quantity);
              // Set a timeout to reset the loading state
              setTimeout(() => {
                setIsAddingToCart(false);
              }, 800);
            }}
          >
            <FiShoppingBag size={18} />
            <span>{isAddingToCart ? 'Adding...' : 'Add to Cart'}</span>
          </button>

          {/* Divider */}
          <div className="h-px bg-gray-200 my-8"></div>

          {/* Categories */}
          {product.categories && product.categories.length > 0 && (
            <div className="mb-6">
              <h3 className="font-medium text-gray-800 mb-2 flex items-center gap-2">
                <FiList size={16} /> Categories
              </h3>
              <div className="flex flex-wrap gap-2">
                {product.categories.map(category => (
                  <span
                    key={category.id}
                    className="bg-gray-100 text-gray-800 px-3 py-1 rounded-full text-sm"
                  >
                    {category.name}
                  </span>
                ))}
              </div>
            </div>
          )}


        </div>
      </div>

      {/* Divider */}
      <div className="h-px bg-gray-200 my-12"></div>

      {/* Product Description */}
      <div className="mb-12">
        <h2 className="text-2xl font-medium text-gray-800 mb-6 flex items-center gap-2">
          <FiInfo size={20} /> Product Description
        </h2>
        <div
          className="prose max-w-none text-gray-700 leading-relaxed"
          dangerouslySetInnerHTML={{ __html: product.description }}
        />
      </div>

      {/* Product Listings */}
      {product.listings && product.listings.length > 0 && (
        <div className="mb-12">
          <ProductListings listings={product.listings} />
        </div>
      )}

      {/* Divider */}
      <div className="h-px bg-gray-200 my-12"></div>

      {/* Reviews Section */}
      <div>
        <h2 className="text-2xl font-medium text-gray-800 mb-6 flex items-center gap-2">
          <FiStar size={20} /> Customer Reviews
        </h2>
        <ProductReviews productId={product.id} />
      </div>
    </div>
  );
};

export default ProductPage;

