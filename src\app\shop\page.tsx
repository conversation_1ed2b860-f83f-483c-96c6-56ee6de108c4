import { Metadata } from "next";
import ShopPageClient from "@/components/shop/ShopPageClient";

// Types
interface Product {
  id: number;
  sku: string;
  name: string;
  slug: string;
  imageUrl: string;
  price: number;
  salePrice?: number;
  inStock: boolean;
  created_at: string;
}

interface MainCategory {
  id: number;
  name: string;
  slug: string;
  imageUrl: string;
  count: number;
}

interface ShopPageData {
  products: {
    pagination: {
      total: number;
      page: number;
      limit: number;
    };
    data: Product[];
  };
  mainCategories: MainCategory[];
}

// Server-side data fetching
async function getShopPageData(page: number = 1, limit: number = 20): Promise<ShopPageData | null> {
  try {
    // Use the correct base URL for both development and production
    const baseUrl = process.env.NODE_ENV === 'production'
      ? process.env.NEXTAUTH_URL || process.env.VERCEL_URL || 'http://localhost:3000'
      : 'http://localhost:3000';

    console.log('Fetching shop data from:', baseUrl);

    // Fetch products and main categories in parallel
    const [productsResponse, categoriesResponse] = await Promise.all([
      fetch(`${baseUrl}/api/shop/products?page=${page}&limit=${limit}`, {
        next: { revalidate: 3600 }, // 1 hour cache
        headers: {
          'Content-Type': 'application/json',
        }
      }),
      fetch(`${baseUrl}/api/shop/main-categories`, {
        next: { revalidate: 86400 }, // 24 hours cache
        headers: {
          'Content-Type': 'application/json',
        }
      })
    ]);

    console.log('Response status:', {
      products: productsResponse.status,
      categories: categoriesResponse.status
    });

    if (!productsResponse.ok || !categoriesResponse.ok) {
      console.error('Failed to fetch shop data:', {
        products: {
          status: productsResponse.status,
          statusText: productsResponse.statusText,
          url: productsResponse.url
        },
        categories: {
          status: categoriesResponse.status,
          statusText: categoriesResponse.statusText,
          url: categoriesResponse.url
        }
      });

      // Try to get error details
      if (!productsResponse.ok) {
        const errorText = await productsResponse.text();
        console.error('Products API error:', errorText.substring(0, 500));
      }
      if (!categoriesResponse.ok) {
        const errorText = await categoriesResponse.text();
        console.error('Categories API error:', errorText.substring(0, 500));
      }

      return null;
    }

    // Check if responses are actually JSON
    const productsContentType = productsResponse.headers.get('content-type');
    const categoriesContentType = categoriesResponse.headers.get('content-type');

    if (!productsContentType?.includes('application/json')) {
      console.error('Products API returned non-JSON response:', productsContentType);
      const text = await productsResponse.text();
      console.error('Products response preview:', text.substring(0, 200));
      return null;
    }

    if (!categoriesContentType?.includes('application/json')) {
      console.error('Categories API returned non-JSON response:', categoriesContentType);
      const text = await categoriesResponse.text();
      console.error('Categories response preview:', text.substring(0, 200));
      return null;
    }

    const [products, categories] = await Promise.all([
      productsResponse.json(),
      categoriesResponse.json()
    ]);

    console.log('Successfully fetched shop data:', {
      productsCount: products?.data?.length || 0,
      categoriesCount: categories?.data?.length || 0
    });

    return {
      products,
      mainCategories: categories.data || []
    };
  } catch (error) {
    console.error('Error fetching shop page data:', error);

    // Log more details about the error
    if (error instanceof SyntaxError && error.message.includes('Unexpected token')) {
      console.error('JSON parsing error - API likely returned HTML instead of JSON');
    }

    return null;
  }
}

// Generate metadata
export const metadata: Metadata = {
  title: 'Shop - Premium Organic Beauty Products | CocoJojo',
  description: 'Discover our complete collection of premium organic beauty products. Shop natural skincare, essential oils, and wellness products with fast shipping.',
  keywords: 'organic beauty, natural skincare, essential oils, premium cosmetics, wellness products',
  openGraph: {
    title: 'Shop - Premium Organic Beauty Products | CocoJojo',
    description: 'Discover our complete collection of premium organic beauty products. Shop natural skincare, essential oils, and wellness products with fast shipping.',
    type: "website",
    url: "/shop",
    images: [
      {
        url: "/images/shop-og.jpg",
        width: 1200,
        height: 630,
        alt: "CocoJojo Shop - Premium Organic Beauty Products",
      },
    ],
  },
  twitter: {
    card: "summary_large_image",
    title: 'Shop - Premium Organic Beauty Products | CocoJojo',
    description: 'Discover our complete collection of premium organic beauty products.',
  },
  alternates: {
    canonical: "/shop",
  },
};

export default async function ShopPage({
  searchParams,
}: {
  searchParams: { page?: string; limit?: string };
}) {
  // Get pagination parameters from URL
  const page = parseInt(searchParams.page || '1');
  const limit = parseInt(searchParams.limit || '20');

  // Fetch data server-side for SEO with error handling
  let data: ShopPageData | null = null;

  try {
    data = await getShopPageData(page, limit);
  } catch (error) {
    console.error('Failed to load shop data in ShopPage:', error);
  }

  if (!data) {
    // Return a fallback UI with empty data structure
    const fallbackData: ShopPageData = {
      products: {
        data: [],
        pagination: {
          page: 1,
          limit: 20,
          total: 0,
          totalPages: 0,
          hasNextPage: false,
          hasPrevPage: false
        }
      },
      mainCategories: []
    };

    return (
      <div className="min-h-screen bg-gray-50">
        <div className="container mx-auto px-4 py-8">
          <div className="text-center mb-8">
            <h1 className="text-3xl font-bold text-gray-900 mb-4">Shop</h1>
            <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-6 max-w-lg mx-auto">
              <div className="flex items-center justify-center mb-3">
                <svg className="w-6 h-6 text-yellow-600 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z" />
                </svg>
                <h2 className="text-lg font-semibold text-yellow-800">Shop Temporarily Unavailable</h2>
              </div>
              <p className="text-yellow-700 mb-4">
                We're experiencing technical difficulties loading our product catalog.
                Our team is working to resolve this issue.
              </p>
              <p className="text-sm text-yellow-600">
                Please try refreshing the page or check back in a few minutes.
              </p>
            </div>
          </div>

          {/* Render the shop component with empty data to maintain layout */}
          <ShopPageClient
            initialProducts={fallbackData.products}
            mainCategories={fallbackData.mainCategories}
          />
        </div>
      </div>
    );
  }

  return (
    <>
      {/* JSON-LD Structured Data */}
      <script
        type="application/ld+json"
        dangerouslySetInnerHTML={{
          __html: JSON.stringify({
            "@context": "https://schema.org",
            "@type": "CollectionPage",
            "name": "Shop - Premium Organic Beauty Products",
            "description": "Discover our complete collection of premium organic beauty products",
            "url": "https://cocojojo.com/shop",
            "mainEntity": {
              "@type": "ItemList",
              "numberOfItems": data.products.pagination.total,
              "itemListElement": data.products.data.slice(0, 10).map((product: Product, index: number) => ({
                "@type": "Product",
                "position": index + 1,
                "name": product.name,
                "url": `https://cocojojo.com/product/${product.slug}`,
                "image": product.imageUrl,
                "offers": {
                  "@type": "Offer",
                  "price": product.salePrice || product.price,
                  "priceCurrency": "USD",
                  "availability": product.inStock ? "https://schema.org/InStock" : "https://schema.org/OutOfStock"
                }
              }))
            },
            "breadcrumb": {
              "@type": "BreadcrumbList",
              "itemListElement": [
                {
                  "@type": "ListItem",
                  "position": 1,
                  "name": "Home",
                  "item": "https://cocojojo.com"
                },
                {
                  "@type": "ListItem",
                  "position": 2,
                  "name": "Shop",
                  "item": "https://cocojojo.com/shop"
                }
              ]
            }
          })
        }}
      />

      <ShopPageClient
        initialProducts={data.products}
        mainCategories={data.mainCategories}
      />
    </>
  );
}

// Enable ISR
export const revalidate = 3600; // 1 hour
