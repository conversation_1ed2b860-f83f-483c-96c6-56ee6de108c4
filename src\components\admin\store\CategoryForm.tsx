"use client";

import { useState, useEffect } from "react";
import { useCategoryStore } from "@/hooks/useCategoryStore";
import { Category } from "@/services/api";
import { FiAlertTriangle } from "react-icons/fi";

interface CategoryFormProps {
  mode: "add" | "edit";
  onClose: () => void;
}

const CategoryForm = ({ mode, onClose }: CategoryFormProps) => {
  const { selectedCategory, createCategory, updateCategory, setSelectedCategory, error: storeError, clearError } = useCategoryStore();

  const [formData, setFormData] = useState<Category>({
    name: "",
    slug: "",
    description: "",
    imageUrl: "",
  });

  const [isSubmitting, setIsSubmitting] = useState(false);
  const [formError, setFormError] = useState<string | null>(null);
  const [slugEdited, setSlugEdited] = useState(false);

  // Clear errors when component mounts
  useEffect(() => {
    clearError();
    setFormError(null);
  }, [clearError]);

  // Load selected category data when in edit mode
  useEffect(() => {
    if (mode === "edit" && selectedCategory) {
      setFormData({
        name: selectedCategory.name,
        slug: selectedCategory.slug,
        description: selectedCategory.description || "",
        imageUrl: selectedCategory.imageUrl || "",
      });
      setSlugEdited(false);
    }
  }, [mode, selectedCategory]);

  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    const { name, value } = e.target;

    // Mark slug as edited if the user changes it directly
    if (name === "slug") {
      setSlugEdited(true);
    }

    // Auto-generate slug from name if it's a new category or if the slug field hasn't been manually edited
    if (name === "name" && (mode === "add" || !slugEdited)) {
      setFormData({
        ...formData,
        [name]: value,
        slug: value.toLowerCase().replace(/\s+/g, "-").replace(/[^a-z0-9-]/g, ""),
      });
    } else {
      setFormData({
        ...formData,
        [name]: value,
      });
    }
  };

  const validateForm = (): boolean => {
    if (!formData.name.trim()) {
      setFormError("Category name is required");
      return false;
    }

    if (!formData.slug.trim()) {
      setFormError("Category slug is required");
      return false;
    }

    return true;
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    // Clear previous errors
    setFormError(null);
    clearError();

    // Validate form
    if (!validateForm()) {
      return;
    }

    setIsSubmitting(true);

    try {
      if (mode === "add") {
        await createCategory(formData);
      } else if (mode === "edit" && selectedCategory?.id) {
        await updateCategory(selectedCategory.id, formData);
      }

      // Reset form and close
      setFormData({ name: "", slug: "", description: "", imageUrl: "" });
      setSelectedCategory(null);
      onClose();
    } catch (err) {
      setFormError(err instanceof Error ? err.message : "An error occurred");
      console.error("Form submission error:", err);
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleCancel = () => {
    setFormData({ name: "", slug: "", description: "", imageUrl: "" });
    setSelectedCategory(null);
    clearError();
    setFormError(null);
    onClose();
  };

  return (
    <div className="max-w-2xl mx-auto">
      <h2 className="text-xl font-semibold mb-6">
        {mode === "add" ? "Add New Category" : "Edit Category"}
      </h2>

      {formError && (
        <div className="mb-4 p-3 bg-red-100 text-red-700 rounded-md flex items-center">
          <FiAlertTriangle className="mr-2 flex-shrink-0" />
          <span>{formError}</span>
        </div>
      )}

      <form onSubmit={handleSubmit}>
        <div className="mb-4">
          <label htmlFor="name" className="block text-sm font-medium text-gray-700 mb-1">
            Category Name *
          </label>
          <input
            type="text"
            id="name"
            name="name"
            value={formData.name}
            onChange={handleChange}
            required
            className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-main-color"
          />
        </div>

        <div className="mb-4">
          <label htmlFor="slug" className="block text-sm font-medium text-gray-700 mb-1">
            Slug *
          </label>
          <input
            type="text"
            id="slug"
            name="slug"
            value={formData.slug}
            onChange={handleChange}
            required
            className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-main-color"
          />
          <p className="mt-1 text-sm text-gray-500">
            URL-friendly version of the name. Used in URLs.
          </p>
        </div>

        <div className="mb-4">
          <label htmlFor="description" className="block text-sm font-medium text-gray-700 mb-1">
            Description
          </label>
          <textarea
            id="description"
            name="description"
            value={formData.description}
            onChange={handleChange}
            rows={4}
            className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-main-color"
          />
        </div>

        <div className="mb-6">
          <label htmlFor="imageUrl" className="block text-sm font-medium text-gray-700 mb-1">
            Image URL
          </label>
          <input
            type="text"
            id="imageUrl"
            name="imageUrl"
            value={formData.imageUrl}
            onChange={handleChange}
            className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-main-color"
            placeholder="https://example.com/image.jpg"
          />
          <p className="mt-1 text-sm text-gray-500">
            URL to an image for this category. Leave empty for no image.
          </p>
        </div>

        <div className="flex justify-end space-x-4">
          <button
            type="button"
            onClick={handleCancel}
            className="px-4 py-2 border border-gray-300 rounded-md text-gray-700 hover:bg-gray-50"
          >
            Cancel
          </button>
          <button
            type="submit"
            disabled={isSubmitting}
            className="px-4 py-2 bg-main-color text-white rounded-md hover:bg-main-color/90 disabled:opacity-50 disabled:cursor-not-allowed"
          >
            {isSubmitting ? "Saving..." : "Save Category"}
          </button>
        </div>
      </form>
    </div>
  );
};

export default CategoryForm;
