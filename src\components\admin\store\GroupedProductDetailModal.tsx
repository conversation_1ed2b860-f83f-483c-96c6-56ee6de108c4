"use client";

import React, { useEffect, useState } from "react";
import { Product, StockStatus, AccessLevel, ProductListing, ProductType } from "@/services/api";
import { useProductStore } from "@/hooks/useProductStore";
import { FiX, FiChevronLeft, FiChevronRight, FiGrid, FiList, FiTag, FiPackage, FiLock, FiUnlock, FiEye, FiEyeOff, FiEdit2 } from "react-icons/fi";
import ProductListingsViewer from "./ProductListingsViewer";
import "@/styles/rich-text-content.css";
import VariantProductForm from "./VariantProductForm";

interface GroupedProductDetailModalProps {
  productId: number | null;
  isOpen: boolean;
  onClose: () => void;
  onEdit: (product: Product) => void;
}

interface TagObject {
  id?: number;
  name: string;
  slug?: string;
}

interface GroupedProductData {
  id?: number;
  sku: string;
  name: string;
  description: string;
  shortDescription: string;
  price: string;
  salePrice?: string;
  stockQuantity?: number;
  stockStatus: StockStatus;
  type?: string;
  access?: AccessLevel;
  password?: string;
  images: Array<{ url: string; position?: number }>;
  categories?: Array<{ id: number; name: string }>;
  categoryIds?: number[];
  tags?: Array<string | TagObject>;
  createdAt?: string;
  updatedAt?: string;

  // Additional properties specific to grouped products
  ProductAttribute?: Array<{
    id: number;
    productId: number;
    attributeId: number;
    attribute: {
      id: number;
      name: string;
    };
    values: Array<{
      id: number;
      productAttributeId: number;
      value: string;
    }>;
  }>;
  variants?: Array<{
    id: number;
    sku: string;
    price: string;
    salePrice: string | null;
    stockQuantity: number | null;
    stockStatus: StockStatus;
    productId: number;
    ProductImage: Array<{
      url: string;
    }>;
    attributes: Array<{
      value: {
        id: number;
        value: string;
      };
    }>;
  }>;
  listings?: Array<{
    id: number;
    title: string;
    content: string;
    productId: number;
    createdAt?: string;
    updatedAt?: string;
  }>;
}

// If you need to use Product type elsewhere, you can create a type that combines them
type ProductWithObjectTags = Omit<Product, 'tags'> & {
  tags?: Array<string | TagObject>;
};

const GroupedProductDetailModal = ({ productId, isOpen, onClose, onEdit }: GroupedProductDetailModalProps) => {
  const [product, setProduct] = useState<GroupedProductData | null>(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [currentImageIndex, setCurrentImageIndex] = useState(0);
  const [viewMode, setViewMode] = useState<'grid' | 'list'>('grid');
  const [selectedVariant, setSelectedVariant] = useState<number | null>(null);
  const [isVariantEditModalOpen, setIsVariantEditModalOpen] = useState(false);
  const { fetchGroupedProductById, setSelectedProduct } = useProductStore();

  useEffect(() => {
    const fetchProduct = async () => {
      if (!productId) return;

      setLoading(true);
      setError(null);

      try {
        // For grouped products, use the store method that uses our API endpoint
        console.log(`Fetching grouped product with ID: ${productId}`);

        // Use the fetchGroupedProductById method from the store
        const groupedProduct = await fetchGroupedProductById(productId);
        console.log(`Grouped product data retrieved successfully:`, groupedProduct);

        if (groupedProduct) {
          setProduct(groupedProduct as GroupedProductData);
          setCurrentImageIndex(0);
          setSelectedVariant(null);
        } else {
          throw new Error(`Failed to fetch grouped product with ID ${productId}`);
        }
      } catch (err) {
        console.error("Error fetching grouped product:", err);
        setError(err instanceof Error ? err.message : "An error occurred");
      } finally {
        setLoading(false);
      }
    };

    if (isOpen && productId) {
      fetchProduct();
    }
  }, [isOpen, productId, fetchGroupedProductById]);

  if (!isOpen) return null;

  const handleNextImage = () => {
    if (!product?.images?.length) return;
    setCurrentImageIndex((prev) => (prev + 1) % product.images.length);
  };

  const handlePrevImage = () => {
    if (!product?.images?.length) return;
    setCurrentImageIndex((prev) => (prev - 1 + product.images.length) % product.images.length);
  };

  // Handle edit button click
  const handleEdit = () => {
    if (!product) return;
    
    // Convert the product to match Product type
    const productForEdit: Product = {
      ...product,
      stockQuantity: product.stockQuantity ?? null,
      type: ProductType.GROUPED,
      tags: product.tags?.map(tag => typeof tag === 'string' ? tag : tag.name) || [],
      images: product.images.map((img, index) => ({
        ...img,
        position: img.position ?? index
      })),
      categories: product.categories?.map(cat => ({
        id: cat.id,
        name: cat.name,
        slug: cat.name.toLowerCase().replace(/\s+/g, '-'),
        description: ''
      })) || []
    };
    
    setSelectedProduct(productForEdit);
    const productData: Product = {
      id: product.id,
      sku: product.sku,
      name: product.name,
      description: product.description,
      shortDescription: product.shortDescription,
      price: product.price,
      salePrice: product.salePrice,
      stockQuantity: product.stockQuantity ?? null,
      stockStatus: product.stockStatus,
      type: ProductType.GROUPED,
      tags: product.tags?.map(tag => typeof tag === 'string' ? tag : tag.name) || [],
      images: product.images.map((img, index) => ({
        ...img,
        position: img.position ?? index
      })),
      categories: product.categories?.map(cat => ({
        id: cat.id,
        name: cat.name,
        slug: cat.name.toLowerCase().replace(/\s+/g, '-'),
        description: ''
      })) || [],
      categoryIds: product.categoryIds || []
    };
    onEdit(productData);
  };

  const handleVariantEdit = (variantId: number) => {
    setSelectedVariant(variantId);
    setIsVariantEditModalOpen(true);
  };

  const handleVariantEditClose = () => {
    setIsVariantEditModalOpen(false);
    setSelectedVariant(null);
    // Refresh the product data
    if (productId) {
      fetchGroupedProductById(productId).then(updatedProduct => {
        if (updatedProduct) {
          setProduct(updatedProduct as GroupedProductData);
        }
      });
    }
  };

  const getStockStatusClass = (status?: StockStatus) => {
    if (!status) return "bg-gray-100 text-gray-800";

    switch (status) {
      case StockStatus.IN_STOCK:
        return "bg-green-100 text-green-800";
      case StockStatus.OUT_OF_STOCK:
        return "bg-red-100 text-red-800";
      case StockStatus.ON_BACKORDER:
        return "bg-yellow-100 text-yellow-800";
      default:
        return "bg-gray-100 text-gray-800";
    }
  };

  const getAccessLevelInfo = (access?: string) => {
    if (!access) return { icon: FiEye, color: "bg-gray-100 text-gray-800", label: "Public" };

    switch (access) {
      case AccessLevel.PUBLIC:
        return { icon: FiEye, color: "bg-green-100 text-green-800", label: "Public" };
      case AccessLevel.PRIVATE:
        return { icon: FiEyeOff, color: "bg-red-100 text-red-800", label: "Private" };
      case AccessLevel.PROTECTED:
        return { icon: FiLock, color: "bg-yellow-100 text-yellow-800", label: "Protected" };
      default:
        return { icon: FiEye, color: "bg-gray-100 text-gray-800", label: "Public" };
    }
  };

  const getVariantImage = (variant: any) => {
    return variant.ProductImage && variant.ProductImage.length > 0
      ? variant.ProductImage[0].url
      : product?.images && product.images.length > 0
        ? product.images[0].url
        : null;
  };

  const getAttributeValueForVariant = (variant: any, attributeName: string) => {
    if (!variant.attributes || !product?.ProductAttribute) return 'N/A';

    const attribute = product.ProductAttribute.find(attr => attr.attribute.name === attributeName);
    if (!attribute) return 'N/A';

    const variantAttribute = variant.attributes.find((attr: any) =>
      attribute.values.some(val => val.id === attr.value.id)
    );

    return variantAttribute ? variantAttribute.value.value : 'N/A';
  };

  return (
    <div className="fixed inset-0 z-50 overflow-y-auto" aria-labelledby="grouped-product-detail-modal" role="dialog" aria-modal="true">
      <div className="flex items-center justify-center min-h-screen pt-4 px-4 pb-20 text-center sm:block sm:p-0">
        <div className="fixed inset-0 bg-gray-500 bg-opacity-75 transition-opacity" aria-hidden="true" onClick={onClose}></div>

        {/* Modal Panel */}
        <div className="inline-block align-bottom bg-white rounded-lg text-left overflow-hidden shadow-xl transform transition-all sm:my-8 sm:align-middle sm:max-w-5xl sm:w-full">
          {/* Header buttons */}
          <div className="absolute top-4 right-4 flex space-x-2 z-10">
            {/* Edit button */}
            <button
              onClick={handleEdit}
              className="text-blue-500 hover:text-blue-700 focus:outline-none"
              title="Edit Product"
            >
              <FiEdit2 className="h-6 w-6" />
            </button>

            {/* Close button */}
            <button
              onClick={onClose}
              className="text-gray-400 hover:text-gray-500 focus:outline-none"
              title="Close"
            >
              <FiX className="h-6 w-6" />
            </button>
          </div>

          {loading ? (
            <div className="flex justify-center items-center h-96">
              <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-main-color"></div>
            </div>
          ) : error ? (
            <div className="p-6 text-center text-red-500">
              <p>{error}</p>
              <button
                onClick={onClose}
                className="mt-4 bg-main-color text-white px-4 py-2 rounded-md hover:bg-main-color/90 transition"
              >
                Close
              </button>
            </div>
          ) : product ? (
            <div className="flex flex-col">
              {/* Product Header */}
              <div className="bg-gray-50 p-6 border-b border-gray-200">
                <div className="flex flex-col md:flex-row md:items-center md:justify-between">
                  <div>
                    <div className="flex items-center">
                      <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-indigo-100 text-indigo-800 mr-2">
                        <FiPackage className="mr-1" /> Grouped Product
                      </span>
                      <h2 className="text-2xl font-bold text-gray-900">{product.name}</h2>
                    </div>
                    <div className="flex items-center mt-2 space-x-2">
                      {product.access && (
                        <span className={`px-3 py-1 inline-flex items-center text-xs leading-5 font-semibold rounded-full ${getAccessLevelInfo(product.access).color}`}>
                          {React.createElement(getAccessLevelInfo(product.access).icon, { className: "mr-1", size: 12 })}
                          {getAccessLevelInfo(product.access).label}

                          {/* Show password if access is PROTECTED */}
                          {product.access === AccessLevel.PROTECTED && (
                            <span className="ml-2 text-xs">
                              (Password: <span className="font-mono bg-gray-50 px-1 rounded">{product.password || "None"}</span>)
                            </span>
                          )}
                        </span>
                      )}
                      <span className={`px-3 py-1 inline-flex text-xs leading-5 font-semibold rounded-full ${getStockStatusClass(product.stockStatus)}`}>
                        {product.stockStatus ? product.stockStatus.replace('_', ' ') : 'Unknown'}
                      </span>
                    </div>
                    <div
                      className="mt-1 text-sm text-gray-500 rich-text-content"
                      dangerouslySetInnerHTML={{ __html: product.shortDescription || '' }}
                    />
                  </div>
                  <div className="mt-4 md:mt-0 flex items-center">
                    <span className="text-xl font-bold text-main-color mr-4">${product.price}</span>
                  </div>
                </div>
              </div>

              {/* Main Content */}
              <div className="flex flex-col md:flex-row">
                {/* Product Images */}
                <div className="md:w-1/3 bg-gray-50 relative">
                  <div className="aspect-w-1 aspect-h-1 w-full">
                    {product.images && product.images.length > 0 ? (
                      <div className="relative h-full">
                        <img
                          src={product.images[currentImageIndex].url}
                          alt={product.name}
                          className="w-full h-full object-contain p-4"
                        />

                        {/* Image navigation */}
                        {product.images.length > 1 && (
                          <>
                            <button
                              onClick={handlePrevImage}
                              className="absolute left-2 top-1/2 -translate-y-1/2 bg-white rounded-full p-2 shadow-md hover:bg-gray-100"
                            >
                              <FiChevronLeft className="h-5 w-5 text-gray-600" />
                            </button>
                            <button
                              onClick={handleNextImage}
                              className="absolute right-2 top-1/2 -translate-y-1/2 bg-white rounded-full p-2 shadow-md hover:bg-gray-100"
                            >
                              <FiChevronRight className="h-5 w-5 text-gray-600" />
                            </button>
                          </>
                        )}

                        {/* Image counter */}
                        {product.images.length > 1 && (
                          <div className="absolute bottom-4 left-0 right-0 flex justify-center">
                            <div className="bg-black bg-opacity-50 rounded-full px-3 py-1 text-white text-xs">
                              {currentImageIndex + 1} / {product.images.length}
                            </div>
                          </div>
                        )}
                      </div>
                    ) : (
                      <div className="w-full h-full flex items-center justify-center bg-gray-200 text-gray-500">
                        No image available
                      </div>
                    )}
                  </div>

                  {/* Thumbnails */}
                  {product.images && product.images.length > 1 && (
                    <div className="flex overflow-x-auto p-2 space-x-2 mt-2">
                      {product.images.map((image, index) => (
                        <button
                          key={index}
                          onClick={() => setCurrentImageIndex(index)}
                          className={`flex-shrink-0 w-16 h-16 rounded-md overflow-hidden border-2 ${
                            currentImageIndex === index ? 'border-main-color' : 'border-transparent'
                          }`}
                        >
                          <img
                            src={image.url}
                            alt={`Thumbnail ${index + 1}`}
                            className="w-full h-full object-cover"
                          />
                        </button>
                      ))}
                    </div>
                  )}
                </div>

                {/* Product Details */}
                <div className="md:w-2/3 p-6 max-h-[80vh] overflow-y-auto">
                  {/* Product Description */}
                  <div className="mb-6">
                    <h3 className="text-lg font-medium text-gray-900 mb-2">Description</h3>
                    <div
                      className="prose prose-sm max-w-none text-gray-700 rich-text-content"
                      dangerouslySetInnerHTML={{ __html: product.description || '' }}
                    />
                  </div>

                  {/* Product Attributes */}
                  <div className="mb-6">
                    <h3 className="text-lg font-medium text-gray-900 mb-2">Attributes</h3>
                    {product.ProductAttribute && product.ProductAttribute.length > 0 ? (
                      <div className="grid grid-cols-2 gap-4">
                        {product.ProductAttribute.map(attr => (
                          <div key={attr.id} className="bg-gray-50 p-4 rounded-lg">
                            <h4 className="font-medium text-gray-700 mb-2">{attr.attribute.name}</h4>
                            <div className="flex flex-wrap gap-2">
                              {attr.values.map(value => (
                                <span key={value.id} className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                                  {value.value}
                                </span>
                              ))}
                            </div>
                          </div>
                        ))}
                      </div>
                    ) : (
                      <p className="text-gray-500">No attributes defined</p>
                    )}
                  </div>

                  {/* Product Variants */}
                  <div>
                    <div className="flex justify-between items-center mb-4">
                      <h3 className="text-lg font-medium text-gray-900">Variants ({product.variants?.length || 0})</h3>
                      <div className="flex space-x-2">
                        <button
                          onClick={() => setViewMode('grid')}
                          className={`p-2 rounded-md ${viewMode === 'grid' ? 'bg-gray-200' : 'bg-gray-100 hover:bg-gray-200'}`}
                          title="Grid View"
                        >
                          <FiGrid className="h-5 w-5 text-gray-700" />
                        </button>
                        <button
                          onClick={() => setViewMode('list')}
                          className={`p-2 rounded-md ${viewMode === 'list' ? 'bg-gray-200' : 'bg-gray-100 hover:bg-gray-200'}`}
                          title="List View"
                        >
                          <FiList className="h-5 w-5 text-gray-700" />
                        </button>
                      </div>
                    </div>

                    {product.variants && product.variants.length > 0 ? (
                      viewMode === 'grid' ? (
                        <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4">
                          {product.variants.map(variant => (
                            <div
                              key={variant.id}
                              className={`border rounded-lg overflow-hidden hover:shadow-md transition ${
                                selectedVariant === variant.id ? 'ring-2 ring-main-color' : ''
                              }`}
                              onClick={() => handleVariantEdit(variant.id)}
                            >
                              <div className="aspect-w-1 aspect-h-1 w-full bg-gray-200">
                                {getVariantImage(variant) ? (
                                  <img
                                    src={getVariantImage(variant)}
                                    alt={variant.sku}
                                    className="w-full h-full object-cover"
                                  />
                                ) : (
                                  <div className="w-full h-full flex items-center justify-center text-gray-500">
                                    No image
                                  </div>
                                )}
                              </div>
                              <div className="p-4">
                                <div className="flex items-center justify-between mb-2">
                                  <span className="text-sm font-medium text-gray-900">${variant.price}</span>
                                  <span className={`px-2 py-0.5 inline-flex text-xs leading-5 font-semibold rounded-full ${getStockStatusClass(variant.stockStatus)}`}>
                                    {variant.stockStatus.replace('_', ' ')}
                                  </span>
                                </div>
                                <div className="text-xs text-gray-500 mb-2">
                                  <span className="flex items-center">
                                    <FiTag className="mr-1" /> {variant.sku}
                                  </span>
                                </div>
                                {product.ProductAttribute && (
                                  <div className="space-y-1">
                                    {product.ProductAttribute.map(attr => (
                                      <div key={attr.id} className="flex justify-between text-xs">
                                        <span className="text-gray-500">{attr.attribute.name}:</span>
                                        <span className="font-medium">{getAttributeValueForVariant(variant, attr.attribute.name)}</span>
                                      </div>
                                    ))}
                                  </div>
                                )}
                                {variant.stockQuantity !== null && (
                                  <div className="mt-2 text-xs text-gray-500">
                                    Stock: {variant.stockQuantity}
                                  </div>
                                )}
                              </div>
                            </div>
                          ))}
                        </div>
                      ) : (
                        <div className="overflow-x-auto">
                          <table className="min-w-full divide-y divide-gray-200">
                            <thead className="bg-gray-50">
                              <tr>
                                <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                  Image
                                </th>
                                <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                  SKU
                                </th>
                                {product.ProductAttribute?.map(attr => (
                                  <th key={attr.id} scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                    {attr.attribute.name}
                                  </th>
                                ))}
                                <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                  Price
                                </th>
                                <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                  Stock
                                </th>
                              </tr>
                            </thead>
                            <tbody className="bg-white divide-y divide-gray-200">
                              {product.variants.map(variant => (
                                <tr
                                  key={variant.id}
                                  className={`hover:bg-gray-50 cursor-pointer ${
                                    selectedVariant === variant.id ? 'bg-blue-50' : ''
                                  }`}
                                  onClick={() => handleVariantEdit(variant.id)}
                                >
                                  <td className="px-6 py-4 whitespace-nowrap">
                                    <div className="h-12 w-12 rounded-md overflow-hidden bg-gray-100">
                                      {getVariantImage(variant) ? (
                                        <img
                                          src={getVariantImage(variant)}
                                          alt={variant.sku}
                                          className="h-12 w-12 object-cover"
                                        />
                                      ) : (
                                        <div className="h-12 w-12 flex items-center justify-center text-gray-400 text-xs">
                                          No image
                                        </div>
                                      )}
                                    </div>
                                  </td>
                                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                    {variant.sku}
                                  </td>
                                  {product.ProductAttribute?.map(attr => (
                                    <td key={attr.id} className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                      {getAttributeValueForVariant(variant, attr.attribute.name)}
                                    </td>
                                  ))}
                                  <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
                                    ${variant.price}
                                    {variant.salePrice && (
                                      <span className="ml-2 line-through text-gray-400">${variant.salePrice}</span>
                                    )}
                                  </td>
                                  <td className="px-6 py-4 whitespace-nowrap">
                                    <div className="flex flex-col">
                                      <span className={`px-2 inline-flex text-xs leading-5 font-semibold rounded-full ${getStockStatusClass(variant.stockStatus)}`}>
                                        {variant.stockStatus.replace('_', ' ')}
                                      </span>
                                      {variant.stockQuantity !== null && (
                                        <span className="mt-1 text-xs text-gray-500">
                                          Qty: {variant.stockQuantity}
                                        </span>
                                      )}
                                    </div>
                                  </td>
                                </tr>
                              ))}
                            </tbody>
                          </table>
                        </div>
                      )
                    ) : (
                      <p className="text-gray-500">No variants available</p>
                    )}
                  </div>
                </div>
              </div>

              {/* Product Listings */}
              {product.listings && product.listings.length > 0 && (
                <div className="px-6 py-4 border-t border-gray-200">
                  <h3 className="text-lg font-medium text-gray-900 mb-4">Additional Information</h3>
                  <ProductListingsViewer listings={product.listings} />
                </div>
              )}

              {/* Tags Section */}
              {product.tags && product.tags.length > 0 && (
                <div className="px-6 py-4 border-t border-gray-200">
                  <h3 className="text-sm font-medium text-gray-700 mb-2">Tags</h3>
                  <div className="flex flex-wrap gap-2">
                    {product.tags.map((tag, index) => (
                      <span key={index} className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-gray-100 text-gray-800">
                        <FiTag className="mr-1" size={12} />
                        {typeof tag === 'string' ? tag : tag.name}
                      </span>
                    ))}
                  </div>
                </div>
              )}

              {/* Footer */}
              <div className="bg-gray-50 px-6 py-3 border-t border-gray-200">
                <div className="flex justify-between text-sm text-gray-500">
                  <div className="flex space-x-4">
                    <span>SKU: {product.sku}</span>
                    {product.categories && product.categories.length > 0 && (
                      <span>
                        Categories: {product.categories.map(c => c.name).join(', ')}
                      </span>
                    )}
                  </div>
                  <div className="flex space-x-4">
                    <span>Created: {new Date(product.createdAt || '').toLocaleDateString()}</span>
                    {product.updatedAt && (
                      <span>Updated: {new Date(product.updatedAt).toLocaleDateString()}</span>
                    )}
                  </div>
                </div>
              </div>
            </div>
          ) : (
            <div className="p-6 text-center text-gray-500">
              <p>No product data available</p>
            </div>
          )}
        </div>
      </div>

      {isVariantEditModalOpen && selectedVariant && product && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <VariantProductForm
            productId={product.id!}
            variantId={selectedVariant}
            initialData={(() => {
              const variant = product.variants?.find((v: any) => v.id === selectedVariant);
              if (!variant) return undefined;
              
              return {
                sku: variant.sku,
                price: Number(variant.price),
                salePrice: variant.salePrice ? Number(variant.salePrice) : undefined,
                stockQuantity: variant.stockQuantity || 0,
                stockStatus: variant.stockStatus,
                attributeValueIds: variant.attributes.map((a: any) => a.value.id),
                images: variant.ProductImage?.map((img: any) => ({
                  url: img.url,
                  position: 0
                })) || []
              };
            })()}
            onClose={handleVariantEditClose}
          />
        </div>
      )}
    </div>
  );
};

export default GroupedProductDetailModal;








