"use client";

import React, { useEffect, useState } from "react";
import { Product, StockStatus, TaxStatus, ProductType, AccessLevel, ProductListing } from "@/services/api";
import { useProductStore } from "@/hooks/useProductStore";
import { FiX, FiEdit2, FiChevronLeft, FiChevronRight, FiLock, FiUnlock, FiEye, FiEyeOff, FiTag, FiList } from "react-icons/fi";
import GroupedProductDetailModal from "./GroupedProductDetailModal";
import ProductListingsViewer from "./ProductListingsViewer";
import "@/styles/rich-text-content.css";

interface ProductDetailModalProps {
  productId: number | null;
  isOpen: boolean;
  onClose: () => void;
  onEdit: (product: Product) => void;
}

interface TagObject {
  id?: number;
  name: string;
  slug?: string;
}

type ProductWithTags = Omit<Product, 'tags'> & {
  tags?: Array<string | TagObject>;
};

const ProductDetailModal = ({ productId, isOpen, onClose, onEdit }: ProductDetailModalProps) => {
  const [product, setProduct] = useState<ProductWithTags | null>(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [currentImageIndex, setCurrentImageIndex] = useState(0);
  const { fetchProductById } = useProductStore();

  // Check if the product is a grouped product
  const isGroupedProduct = product?.type === ProductType.GROUPED;

  useEffect(() => {
    const fetchProduct = async () => {
      if (!productId) return;

      setLoading(true);
      setError(null);

      try {
        const fetchedProduct = await fetchProductById(productId);
        if (fetchedProduct) {
          setProduct(fetchedProduct);
          setCurrentImageIndex(0);
        } else {
          setError("Failed to load product details");
        }
      } catch (err) {
        setError(err instanceof Error ? err.message : "An error occurred");
      } finally {
        setLoading(false);
      }
    };

    if (isOpen && productId) {
      fetchProduct();
    }
  }, [isOpen, productId, fetchProductById]);

  if (!isOpen) return null;

  // If it's a grouped product, render the GroupedProductDetailModal instead
  if (isGroupedProduct && productId) {
    console.log(`ProductDetailModal: Detected grouped product with ID ${productId}, rendering GroupedProductDetailModal`);
    return (
      <GroupedProductDetailModal
        productId={productId}
        isOpen={isOpen}
        onClose={onClose}
        onEdit={onEdit}
      />
    );
  }

  const handleNextImage = () => {
    if (!product?.images?.length) return;
    setCurrentImageIndex((prev) => (prev + 1) % product.images.length);
  };

  const handlePrevImage = () => {
    if (!product?.images?.length) return;
    setCurrentImageIndex((prev) => (prev - 1 + product.images.length) % product.images.length);
  };

  const getStockStatusClass = (status?: StockStatus) => {
    if (!status) return "bg-gray-100 text-gray-800";

    switch (status) {
      case StockStatus.IN_STOCK:
        return "bg-green-100 text-green-800";
      case StockStatus.OUT_OF_STOCK:
        return "bg-red-100 text-red-800";
      case StockStatus.ON_BACKORDER:
        return "bg-yellow-100 text-yellow-800";
      default:
        return "bg-gray-100 text-gray-800";
    }
  };

  const getAccessLevelInfo = (access?: string) => {
    if (!access) return { icon: FiEye, color: "bg-gray-100 text-gray-800", label: "Public" };

    switch (access) {
      case AccessLevel.PUBLIC:
        return { icon: FiEye, color: "bg-green-100 text-green-800", label: "Public" };
      case AccessLevel.PRIVATE:
        return { icon: FiEyeOff, color: "bg-red-100 text-red-800", label: "Private" };
      case AccessLevel.PROTECTED:
        return { icon: FiLock, color: "bg-yellow-100 text-yellow-800", label: "Protected" };
      default:
        return { icon: FiEye, color: "bg-gray-100 text-gray-800", label: "Public" };
    }
  };

  return (
    <div className="fixed inset-0 z-50 overflow-y-auto" aria-labelledby="product-detail-modal" role="dialog" aria-modal="true">
      <div className="flex items-center justify-center min-h-screen pt-4 px-4 pb-20 text-center sm:block sm:p-0">
        <div className="fixed inset-0 bg-gray-500 bg-opacity-75 transition-opacity" aria-hidden="true" onClick={onClose}></div>

        {/* Modal Panel */}
        <div className="inline-block align-bottom bg-white rounded-lg text-left overflow-hidden shadow-xl transform transition-all sm:my-8 sm:align-middle sm:max-w-4xl sm:w-full">
          {/* Close button */}
          <button
            onClick={onClose}
            className="absolute top-4 right-4 text-gray-400 hover:text-gray-500 focus:outline-none z-10"
          >
            <FiX className="h-6 w-6" />
          </button>

          {loading ? (
            <div className="flex justify-center items-center h-96">
              <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-main-color"></div>
            </div>
          ) : error ? (
            <div className="p-6 text-center text-red-500">
              <p>{error}</p>
              <button
                onClick={onClose}
                className="mt-4 bg-main-color text-white px-4 py-2 rounded-md hover:bg-main-color/90 transition"
              >
                Close
              </button>
            </div>
          ) : product ? (
            <div className="flex flex-col md:flex-row">
              {/* Product Images */}
              <div className="md:w-1/2 bg-gray-50 relative">
                <div className="aspect-w-1 aspect-h-1 w-full">
                  {product.images && product.images.length > 0 ? (
                    <div className="relative h-full">
                      <img
                        src={product.images[currentImageIndex].url}
                        alt={product.name}
                        className="w-full h-full object-contain p-4"
                      />

                      {/* Image navigation */}
                      {product.images.length > 1 && (
                        <>
                          <button
                            onClick={handlePrevImage}
                            className="absolute left-2 top-1/2 -translate-y-1/2 bg-white rounded-full p-2 shadow-md hover:bg-gray-100"
                          >
                            <FiChevronLeft className="h-5 w-5 text-gray-600" />
                          </button>
                          <button
                            onClick={handleNextImage}
                            className="absolute right-2 top-1/2 -translate-y-1/2 bg-white rounded-full p-2 shadow-md hover:bg-gray-100"
                          >
                            <FiChevronRight className="h-5 w-5 text-gray-600" />
                          </button>
                        </>
                      )}

                      {/* Image counter */}
                      {product.images.length > 1 && (
                        <div className="absolute bottom-4 left-0 right-0 flex justify-center">
                          <div className="bg-black bg-opacity-50 rounded-full px-3 py-1 text-white text-xs">
                            {currentImageIndex + 1} / {product.images.length}
                          </div>
                        </div>
                      )}
                    </div>
                  ) : (
                    <div className="w-full h-full flex items-center justify-center bg-gray-200 text-gray-500">
                      No image available
                    </div>
                  )}
                </div>

                {/* Thumbnails */}
                {product.images && product.images.length > 1 && (
                  <div className="flex overflow-x-auto p-2 space-x-2 mt-2">
                    {product.images.map((image, index) => (
                      <button
                        key={index}
                        onClick={() => setCurrentImageIndex(index)}
                        className={`flex-shrink-0 w-16 h-16 rounded-md overflow-hidden border-2 ${
                          currentImageIndex === index ? 'border-main-color' : 'border-transparent'
                        }`}
                      >
                        <img
                          src={image.url}
                          alt={`Thumbnail ${index + 1}`}
                          className="w-full h-full object-cover"
                        />
                      </button>
                    ))}
                  </div>
                )}
              </div>

              {/* Product Details */}
              <div className="md:w-1/2 p-6 max-h-[80vh] overflow-y-auto">
                <div className="flex justify-between items-start">
                  <h2 className="text-2xl font-bold text-gray-900 mb-2">{product.name}</h2>
                </div>

                <div className="flex items-center mb-4">
                  {product.salePrice ? (<>
                <span className="text-2xl font-bold  text-main-color">${product.salePrice}</span>

                    <span className="ml-2 text-lg line-through text-gray-400">${product.price}</span></>
                  ):(
                  <span className="text-2xl font-bold text-main-color">${product.price}</span>)
}

                </div>

                <div className="mb-6 flex flex-wrap gap-2">
                  <span className={`px-3 py-1 inline-flex text-sm leading-5 font-semibold rounded-full ${getStockStatusClass(product.stockStatus)}`}>
                    {product.stockStatus ? product.stockStatus.replace('_', ' ') : 'Unknown'}
                    {product.stockQuantity !== undefined && ` (${product.stockQuantity})`}
                  </span>

                  {product.access && (
                    <span className={`px-3 py-1 inline-flex items-center text-sm leading-5 font-semibold rounded-full ${getAccessLevelInfo(product.access).color}`}>
                      {React.createElement(getAccessLevelInfo(product.access).icon, { className: "mr-1", size: 14 })}
                      {getAccessLevelInfo(product.access).label}
                      {product.access === AccessLevel.PROTECTED && product.password && (
                        <span className="ml-1 text-xs">(Password Protected)</span>
                      )}
                    </span>
                  )}
                </div>

                {product.shortDescription && (
                  <div className="mb-4">
                    <h3 className="text-sm font-medium text-gray-500 uppercase tracking-wider mb-2">Short Description</h3>
                    <div
                      className="text-gray-700 rich-text-content"
                      dangerouslySetInnerHTML={{ __html: product.shortDescription }}
                    />
                  </div>
                )}

                {product.description && (
                  <div className="mb-6">
                    <h3 className="text-sm font-medium text-gray-500 uppercase tracking-wider mb-2">Description</h3>
                    <div
                      className="prose prose-sm max-w-none text-gray-700 rich-text-content"
                      dangerouslySetInnerHTML={{ __html: product.description }}
                    />
                  </div>
                )}

                <div className="grid grid-cols-2 gap-4 mb-6">
                  <div>
                    <h3 className="text-sm font-medium text-gray-500 uppercase tracking-wider mb-2">Product Details</h3>
                    <dl className="space-y-2">
                      <div className="flex justify-between">
                        <dt className="text-sm font-medium text-gray-500">SKU</dt>
                        <dd className="text-sm text-gray-900">{product.sku || 'N/A'}</dd>
                      </div>
                      <div className="flex justify-between">
                        <dt className="text-sm font-medium text-gray-500">Type</dt>
                        <dd className="text-sm text-gray-900">{product.type?.replace('_', ' ') || 'N/A'}</dd>
                      </div>
                      <div className="flex justify-between">
                        <dt className="text-sm font-medium text-gray-500">Tax Status</dt>
                        <dd className="text-sm text-gray-900">{product.taxStatus?.replace('_', ' ') || 'N/A'}</dd>
                      </div>
                      <div className="flex justify-between">
                        <dt className="text-sm font-medium text-gray-500">Tax Class</dt>
                        <dd className="text-sm text-gray-900">{product.taxClass?.replace('_', ' ') || 'N/A'}</dd>
                      </div>
                    </dl>
                  </div>

                  <div>
                    <h3 className="text-sm font-medium text-gray-500 uppercase tracking-wider mb-2">Categories</h3>
                    {product.categories && product.categories.length > 0 ? (
                      <div className="flex flex-wrap gap-2">
                        {product.categories.map(category => (
                          <span key={category.id} className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                            {category.name}
                          </span>
                        ))}
                      </div>
                    ) : (
                      <p className="text-sm text-gray-500">No categories assigned</p>
                    )}
                  </div>
                </div>

                {/* Tags Section */}
                {product.tags && product.tags.length > 0 && (
                  <div className="mb-6">
                    <h3 className="text-sm font-medium text-gray-500 uppercase tracking-wider mb-2">Tags</h3>
                    <div className="flex flex-wrap gap-2">
                      {product.tags.map((tag, index) => (
                        <span key={index} className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-gray-100 text-gray-800">
                          <FiTag className="mr-1" size={12} />
                          {typeof tag === 'string' ? tag : tag.name}
                        </span>
                      ))}
                    </div>
                  </div>
                )}

                {/* Product Listings */}
                {product.listings && product.listings.length > 0 && (
                  <div className="mb-6">
                    <h3 className="text-sm font-medium text-gray-500 uppercase tracking-wider mb-2">Additional Information</h3>
                    <ProductListingsViewer listings={product.listings} />
                  </div>
                )}

                {/* Access Level */}
                {product.access && (
                  <div className="mb-4">
                    <h3 className="text-sm font-medium text-gray-500">Access Level</h3>
                    <div className="mt-1">
                      <span className={`px-3 py-1 inline-flex items-center text-xs leading-5 font-semibold rounded-full ${getAccessLevelInfo(product.access).color}`}>
                        {React.createElement(getAccessLevelInfo(product.access).icon, { className: "mr-1", size: 12 })}
                        {getAccessLevelInfo(product.access).label}
                      </span>

                      {/* Show password if access is PROTECTED */}
                      {product.access === AccessLevel.PROTECTED && (
                        <div className="mt-2">
                          <span className="text-sm font-medium text-gray-500 mr-2">Password:</span>
                          <span className="text-sm font-mono bg-gray-100 px-2 py-1 rounded">
                            {product.password || "No password set"}
                          </span>
                        </div>
                      )}
                    </div>
                  </div>
                )}

                <div className="border-t border-gray-200 pt-4">
                  <div className="flex justify-between text-sm text-gray-500">
                    <span>Created: {new Date(product.createdAt || '').toLocaleDateString()}</span>
                    {product.updatedAt && (
                      <span>Updated: {new Date(product.updatedAt).toLocaleDateString()}</span>
                    )}
                  </div>
                </div>
              </div>
            </div>
          ) : (
            <div className="p-6 text-center text-gray-500">
              <p>No product data available</p>
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default ProductDetailModal;





