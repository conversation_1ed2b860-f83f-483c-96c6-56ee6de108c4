"use client";

import { useEffect, useState } from "react";
import { useProductStore } from "@/hooks/useProductStore";
import { FiEdit2, FiTrash2, FiAlertCircle, FiRefreshCw, FiEye, FiSearch, FiFilter, FiX } from "react-icons/fi";
import DeleteProductModal from "./DeleteProductModal";
import ProductDetailModal from "./ProductDetailModal";
import { Product, StockStatus, ProductType } from "@/services/api";

interface ProductListProps {
  onEdit: (product?: Product) => void;
}

const ProductList = ({ onEdit }: ProductListProps) => {
  const { products, isLoading, error, fetchProducts, setSelectedProduct, clearError } = useProductStore();
  const [isDeleteModalOpen, setIsDeleteModalOpen] = useState(false);
  const [isDetailModalOpen, setIsDetailModalOpen] = useState(false);
  const [productToDelete, setProductToDelete] = useState<Product | null>(null);
  const [selectedProductId, setSelectedProductId] = useState<number | null>(null);
  const [fetchError, setFetchError] = useState<string | null>(null);

  // Search and filter state
  const [searchTerm, setSearchTerm] = useState("");
  const [minPrice, setMinPrice] = useState("");
  const [maxPrice, setMaxPrice] = useState("");
  const [showFilters, setShowFilters] = useState(false);

  // Filtered products
  const filteredProducts = products.filter(product => {
    // Search by name or SKU
    const matchesSearch = searchTerm === "" ||
      product.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
      product.sku.toLowerCase().includes(searchTerm.toLowerCase());

    // Filter by price
    const price = parseFloat(product.price);
    const matchesMinPrice = minPrice === "" || price >= parseFloat(minPrice);
    const matchesMaxPrice = maxPrice === "" || price <= parseFloat(maxPrice);

    return matchesSearch && matchesMinPrice && matchesMaxPrice;
  });

  useEffect(() => {
    const loadProducts = async () => {
      try {
        await fetchProducts();
        setFetchError(null);
      } catch (err) {
        setFetchError(err instanceof Error ? err.message : 'Failed to load products');
      }
    };

    loadProducts();
  }, [fetchProducts]);

  const handleEdit = async (product: Product) => {
    try {
      // First set the selected product with the basic data we have
      setSelectedProduct(product);

      let completeProduct = null;

      // Then fetch the complete product data by ID
      if (product.id) {
        console.log(`Fetching complete product data for ID ${product.id}...`);
        completeProduct = await useProductStore.getState().fetchProductById(product.id);
        console.log(`Fetched complete product data:`, completeProduct);

        // Update the selected product with the complete data
        if (completeProduct) {
          setSelectedProduct(completeProduct);
        }
      }

      // Navigate to the edit form with the product
      const productToEdit = completeProduct || product;
      onEdit(productToEdit);
    } catch (error) {
      console.error(`Error fetching complete product data:`, error);
      // Continue with the edit using the basic product data
      onEdit(product);
    }
  };

  const handleDelete = (product: Product) => {
    setProductToDelete(product);
    setIsDeleteModalOpen(true);
  };

  const closeDeleteModal = () => {
    setIsDeleteModalOpen(false);
    setProductToDelete(null);
  };

  const handleViewDetails = (product: Product) => {
    if (product.id) {
      setSelectedProductId(product.id);
      setIsDetailModalOpen(true);
    }
  };

  const closeDetailModal = () => {
    setIsDetailModalOpen(false);
    setSelectedProductId(null);
  };

  const handleRetry = async () => {
    clearError();
    setFetchError(null);
    try {
      await fetchProducts();
    } catch (err) {
      setFetchError(err instanceof Error ? err.message : 'Failed to load products');
    }
  };

  // Display loading state
  if (isLoading && products.length === 0) {
    return (
      <div className="flex justify-center items-center h-40">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-main-color"></div>
      </div>
    );
  }

  // Display error state
  if ((error || fetchError) && products.length === 0) {
    return (
      <div className="text-center py-8">
        <div className="flex justify-center mb-4">
          <FiAlertCircle className="text-red-500 w-12 h-12" />
        </div>
        <h3 className="text-lg font-medium text-red-800 mb-2">Error loading products</h3>
        <p className="text-red-600 mb-4">{error || fetchError}</p>
        <button
          onClick={handleRetry}
          className="mt-2 bg-main-color text-white px-4 py-2 rounded-md hover:bg-main-color/90 transition flex items-center justify-center mx-auto"
        >
          <FiRefreshCw className="mr-2" /> Retry
        </button>
      </div>
    );
  }

  // Display empty state
  if (products.length === 0) {
    return (
      <div className="text-center py-8 text-gray-500">
        <p>No products found. Add your first product to get started.</p>
      </div>
    );
  }

  const getStockStatusClass = (status?: StockStatus) => {
    if (!status) {
      return "bg-gray-100 text-gray-800";
    }

    switch (status) {
      case StockStatus.IN_STOCK:
        return "bg-green-100 text-green-800";
      case StockStatus.OUT_OF_STOCK:
        return "bg-red-100 text-red-800";
      case StockStatus.ON_BACKORDER:
        return "bg-yellow-100 text-yellow-800";
      default:
        return "bg-gray-100 text-gray-800";
    }
  };

  // Function to get row class based on product type
  const getProductRowClass = (product: Product) => {
    const baseClass = "hover:bg-gray-50 cursor-pointer";

    if (product.type === ProductType.GROUPED) {
      return `${baseClass} bg-indigo-50 border-l-4 border-indigo-500`;
    }

    return baseClass;
  };

  // Reset all filters
  const resetFilters = () => {
    setSearchTerm("");
    setMinPrice("");
    setMaxPrice("");
  };

  return (
    <div>
      {/* Search and Filter UI */}
      <div className="mb-6 bg-white p-4 rounded-lg shadow-sm">
        <div className="flex flex-col md:flex-row gap-4">
          {/* Search input */}
          <div className="flex-1">
            <div className="relative">
              <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                <FiSearch className="text-gray-400" />
              </div>
              <input
                type="text"
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                placeholder="Search by name or SKU..."
                className="block w-full pl-10 pr-3 py-2 border border-gray-300 rounded-md leading-5 bg-white placeholder-gray-500 focus:outline-none focus:ring-main-color focus:border-main-color sm:text-sm"
              />
              {searchTerm && (
                <button
                  onClick={() => setSearchTerm("")}
                  className="absolute inset-y-0 right-0 pr-3 flex items-center"
                >
                  <FiX className="text-gray-400 hover:text-gray-600" />
                </button>
              )}
            </div>
          </div>

          {/* Filter toggle button */}
          <button
            onClick={() => setShowFilters(!showFilters)}
            className="inline-flex items-center px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-main-color"
          >
            <FiFilter className="mr-2 -ml-1 h-5 w-5 text-gray-500" />
            {showFilters ? "Hide Filters" : "Show Filters"}
          </button>
        </div>

        {/* Price filter */}
        {showFilters && (
          <div className="mt-4 pt-4 border-t border-gray-200">
            <h3 className="text-sm font-medium text-gray-700 mb-3">Filter by Price</h3>
            <div className="flex flex-wrap gap-4">
              <div className="w-full sm:w-auto">
                <label htmlFor="min-price" className="block text-sm font-medium text-gray-700 mb-1">
                  Min Price
                </label>
                <input
                  type="number"
                  id="min-price"
                  value={minPrice}
                  onChange={(e) => setMinPrice(e.target.value)}
                  min="0"
                  step="0.01"
                  placeholder="Min"
                  className="block w-full sm:w-32 px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-main-color focus:border-main-color sm:text-sm"
                />
              </div>
              <div className="w-full sm:w-auto">
                <label htmlFor="max-price" className="block text-sm font-medium text-gray-700 mb-1">
                  Max Price
                </label>
                <input
                  type="number"
                  id="max-price"
                  value={maxPrice}
                  onChange={(e) => setMaxPrice(e.target.value)}
                  min="0"
                  step="0.01"
                  placeholder="Max"
                  className="block w-full sm:w-32 px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-main-color focus:border-main-color sm:text-sm"
                />
              </div>
              <div className="w-full sm:w-auto flex items-end">
                <button
                  onClick={resetFilters}
                  className="px-4 py-2 text-sm font-medium text-gray-700 bg-gray-100 hover:bg-gray-200 rounded-md focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-gray-500"
                >
                  Reset Filters
                </button>
              </div>
            </div>
          </div>
        )}
      </div>

      {/* Results count */}
      <div className="mb-4 text-sm text-gray-600">
        Showing {filteredProducts.length} of {products.length} products
      </div>

      <div className="overflow-x-auto">
        <table className="min-w-full divide-y divide-gray-200">
          <thead className="bg-gray-50">
            <tr>
              <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                Image
              </th>
              <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                Name
              </th>
              <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                SKU
              </th>
              <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                Type
              </th>
              <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                Price
              </th>
              <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                Stock
              </th>
              <th scope="col" className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                Actions
              </th>
            </tr>
          </thead>
          <tbody className="bg-white divide-y divide-gray-200">
            {filteredProducts.map((product) => (
              <tr
                key={product.id}
                className={getProductRowClass(product)}
                onClick={() => handleViewDetails(product)}
              >
                <td className="px-6 py-4 whitespace-nowrap">
                  <div className="relative h-12 w-12 rounded overflow-hidden">
                    {product.images && product.images.length > 0 ? (
                      <img
                        src={product.images[0].url}
                        alt={product.name}
                        className="h-12 w-12 object-cover"
                      />
                    ) : (
                      <div className="h-12 w-12 bg-gray-200 flex items-center justify-center text-gray-500 text-xs">
                        No image
                      </div>
                    )}
                  </div>
                </td>
                <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
                  {product.name}
                </td>
                <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                  {product.sku}
                </td>
                <td className="px-6 py-4 whitespace-nowrap">
                  <span className={`px-2 py-1 inline-flex text-xs leading-5 font-semibold rounded-full ${
                    product.type === ProductType.GROUPED
                      ? 'bg-indigo-100 text-indigo-800'
                      : 'bg-blue-100 text-blue-800'
                  }`}>
                    {product.type || ProductType.SIMPLE}
                  </span>
                </td>
                <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                  ${product.price}
                  {product.salePrice && (
                    <span className="ml-2 line-through text-gray-400">${product.salePrice}</span>
                  )}
                </td>
                <td className="px-6 py-4 whitespace-nowrap">
                  <span className={`px-2 inline-flex text-xs leading-5 font-semibold rounded-full ${getStockStatusClass(product.stockStatus || StockStatus.OUT_OF_STOCK)}`}>
                    {product.stockStatus ? product.stockStatus.replace('_', ' ') : 'Unknown'} ({product.stockQuantity || 0})
                  </span>
                </td>
                <td className="px-6 py-4 whitespace-nowrap text-right text-sm font-medium" onClick={(e) => e.stopPropagation()}>
                  <button
                    onClick={() => handleViewDetails(product)}
                    className="text-blue-600 hover:text-blue-900 mr-3"
                    title="View Product Details"
                  >
                    <FiEye className="inline-block" />
                  </button>
                  <button
                    onClick={() => handleEdit(product)}
                    className="text-indigo-600 hover:text-indigo-900 mr-3"
                    title="Edit Product"
                  >
                    <FiEdit2 className="inline-block" />
                  </button>
                  <button
                    onClick={() => handleDelete(product)}
                    className="text-red-600 hover:text-red-900"
                    title="Delete Product"
                  >
                    <FiTrash2 className="inline-block" />
                  </button>
                </td>
              </tr>
            ))}
          </tbody>
        </table>
      </div>

      <DeleteProductModal
        isOpen={isDeleteModalOpen}
        product={productToDelete}
        onClose={closeDeleteModal}
      />

      <ProductDetailModal
        isOpen={isDetailModalOpen}
        productId={selectedProductId}
        onClose={closeDetailModal}
        onEdit={(product) => {
          setSelectedProduct(product);
          closeDetailModal();
          onEdit(product);
        }}
      />
    </div>
  );
};

export default ProductList;
