"use client";

import { useState, useEffect } from 'react';
import { FiX, FiEdit2, FiAlertTriangle } from 'react-icons/fi';
import { Product, ProductVariant } from '@/services/api';
import VariantProductForm from './VariantProductForm';

// Add extended interface to include missing properties

interface VariantSelectionModalProps {
  isOpen: boolean;
  product: Product | null;
  onClose: () => void;
}

export default function VariantSelectionModal({ isOpen, product, onClose }: VariantSelectionModalProps) {
  const [selectedVariant, setSelectedVariant] = useState<ProductVariant | null>(null);
  const [isEditingVariant, setIsEditingVariant] = useState(false);

  useEffect(() => {
    if (!isOpen) {
      setSelectedVariant(null);
      setIsEditingVariant(false);
    }
  }, [isOpen]);

  if (!isOpen || !product) return null;

  const handleEditVariant = (variant: ProductVariant) => {
    setSelectedVariant(variant);
    setIsEditingVariant(true);
  };

  const handleCloseVariantEdit = () => {
    setSelectedVariant(null);
    setIsEditingVariant(false);
  };

  // Cast product to access variants
  const groupedProduct = product as any;
  const variants = groupedProduct.variants || [];

  if (isEditingVariant && selectedVariant) {
    return (
      <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
        <div className="bg-white rounded-lg max-w-4xl w-full mx-4 max-h-[90vh] overflow-y-auto">
          <VariantProductForm
            productId={product.id!}
            variantId={selectedVariant.id!}
            initialData={{
              sku: selectedVariant.sku || '',
              price: typeof selectedVariant.price === 'number' ? selectedVariant.price : parseFloat(selectedVariant.price || '0'),
              salePrice: selectedVariant.salePrice ? (typeof selectedVariant.salePrice === 'number' ? selectedVariant.salePrice : parseFloat(selectedVariant.salePrice)) : undefined,
              saleStart: selectedVariant.saleStart || undefined,
              saleEnd: selectedVariant.saleEnd || undefined,
              stockQuantity: selectedVariant.stockQuantity || 1,
              stockStatus: selectedVariant.stockStatus,
              attributeValueIds: selectedVariant.attributeValueIds || [],
              images: selectedVariant.images?.map(img => ({
                id: img.id,
                url: img.url,
                position: img.position || 0
              })) || []
            }}
            onClose={handleCloseVariantEdit}
          />
        </div>
      </div>
    );
  }

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div className="bg-white rounded-lg max-w-4xl w-full mx-4 max-h-[90vh] overflow-y-auto">
        <div className="p-6">
          <div className="flex justify-between items-center mb-6">
            <h2 className="text-2xl font-semibold text-gray-800">
              Edit Product Variants - {product.name}
            </h2>
            <button
              onClick={onClose}
              className="text-gray-500 hover:text-gray-700"
            >
              <FiX size={24} />
            </button>
          </div>

          {variants.length === 0 ? (
            <div className="text-center py-8">
              <FiAlertTriangle className="mx-auto text-gray-400 mb-4" size={48} />
              <p className="text-gray-600">No variants found for this product.</p>
            </div>
          ) : (
            <div className="space-y-4">
              <p className="text-gray-600 mb-4">
                Select a variant to edit its specific properties like price, stock, and images.
              </p>

              <div className="grid grid-cols-1 gap-4">
                {variants.map((variant: any, index: number) => (
                  <div
                    key={variant.id || index}
                    className="border rounded-lg p-4 hover:bg-gray-50 transition-colors"
                  >
                    <div className="flex justify-between items-start">
                      <div className="flex-1">
                        <div className="flex items-center gap-4 mb-2">
                          <h3 className="font-medium text-gray-900">
                            {variant.sku || `Variant ${index + 1}`}
                          </h3>
                          <span className="text-sm text-gray-500">
                            ${typeof variant.price === 'number' ? variant.price.toFixed(2) : parseFloat(variant.price || '0').toFixed(2)}
                          </span>
                          <span className={`px-2 py-1 text-xs rounded-full ${
                            variant.stockStatus === 'IN_STOCK'
                              ? 'bg-green-100 text-green-800'
                              : variant.stockStatus === 'OUT_OF_STOCK'
                              ? 'bg-red-100 text-red-800'
                              : 'bg-yellow-100 text-yellow-800'
                          }`}>
                            {variant.stockStatus?.replace('_', ' ') || 'Unknown'}
                          </span>
                        </div>

                        {variant.attributes && variant.attributes.length > 0 && (
                          <div className="flex flex-wrap gap-2 mb-2">
                            {variant.attributes.map((attr: any, attrIndex: number) => (
                              <span
                                key={attrIndex}
                                className="px-2 py-1 bg-blue-100 text-blue-800 text-xs rounded"
                              >
                                {attr.value?.value || 'Unknown'}
                              </span>
                            ))}
                          </div>
                        )}

                        <div className="text-sm text-gray-600">
                          Stock: {variant.stockQuantity || 0} units
                        </div>
                      </div>

                      <button
                        onClick={() => handleEditVariant(variant)}
                        className="flex items-center gap-2 px-3 py-2 text-sm bg-main-color text-white rounded-md hover:bg-main-color/90 transition-colors"
                      >
                        <FiEdit2 size={16} />
                        Edit
                      </button>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          )}

          <div className="mt-6 flex justify-end">
            <button
              onClick={onClose}
              className="px-6 py-2 border border-gray-300 rounded-md text-gray-700 hover:bg-gray-50"
            >
              Close
            </button>
          </div>
        </div>
      </div>
    </div>
  );
}


