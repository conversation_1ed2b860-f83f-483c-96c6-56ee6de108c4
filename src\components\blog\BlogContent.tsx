"use client";

import Image from 'next/image';
import { Blog<PERSON>ontentBlock } from '@/types/blog';

interface BlogContentProps {
  content: BlogContentBlock[];
}

export default function BlogContent({ content }: BlogContentProps) {
  const renderBlock = (block: BlogContentBlock, index: number) => {
    switch (block.type) {
      case 'text':
        return (
          <p key={index} className="text-gray-700 leading-relaxed mb-6">
            {block.content}
          </p>
        );

      case 'heading':
        const HeadingTag = `h${block.level || 2}` as keyof JSX.IntrinsicElements;
        const headingClasses = {
          1: 'text-4xl font-bold text-gray-900 mb-8 mt-12',
          2: 'text-3xl font-bold text-gray-900 mb-6 mt-10',
          3: 'text-2xl font-semibold text-gray-900 mb-4 mt-8',
          4: 'text-xl font-semibold text-gray-900 mb-4 mt-6',
          5: 'text-lg font-semibold text-gray-900 mb-3 mt-4',
          6: 'text-base font-semibold text-gray-900 mb-3 mt-4',
        };

        return (
          <HeadingTag
            key={index}
            id={`heading-${index}`}
            className={headingClasses[block.level as keyof typeof headingClasses] || headingClasses[2]}
          >
            {block.content}
          </HeadingTag>
        );

      case 'image':
        return (
          <div key={index} className="my-8">
            <div className="relative w-full h-96 rounded-lg overflow-hidden shadow-lg">
              <Image
                src={block.imageUrl || ''}
                alt={block.imageAlt || 'Blog image'}
                fill
                className="object-cover"
                sizes="(max-width: 768px) 100vw, (max-width: 1200px) 80vw, 70vw"
              />
            </div>
            {block.imageAlt && (
              <p className="text-sm text-gray-500 text-center mt-3 italic">
                {block.imageAlt}
              </p>
            )}
          </div>
        );

      case 'list':
        return (
          <div key={index} className="mb-6">
            {block.content && (
              <p className="text-gray-700 mb-3 font-medium">{block.content}</p>
            )}
            <ul className="list-disc list-inside space-y-2 text-gray-700 ml-4">
              {block.listItems?.map((item, itemIndex) => (
                <li key={itemIndex} className="leading-relaxed">
                  {item}
                </li>
              ))}
            </ul>
          </div>
        );

      case 'quote':
        return (
          <blockquote key={index} className="my-8 p-6 bg-gray-50 border-l-4 border-main-color rounded-r-lg">
            <p className="text-lg text-gray-800 italic leading-relaxed">
              "{block.content}"
            </p>
          </blockquote>
        );

      default:
        return null;
    }
  };

  return (
    <div className="blog-content">
      {content.map((block, index) => renderBlock(block, index))}
    </div>
  );
}
