"use client";

import { useState, useEffect, useMemo } from 'react';
import { useRouter, useSearchParams } from 'next/navigation';
import { FiSearch, FiFilter, FiGrid, FiList, FiX } from 'react-icons/fi';
import { allBlogPosts, getAllTags, getFeaturedPosts } from '@/data/blogData';
import { BlogPost } from '@/types/blog';
import BlogCard from './BlogCard';
import BlogHero from './BlogHero';
import BlogTagFilter from './BlogTagFilter';

interface BlogListingProps {
  searchParams: {
    query?: string;
    tags?: string;
    page?: string;
    sortBy?: string;
    sortOrder?: string;
  };
}

export default function BlogListing({ searchParams }: BlogListingProps) {
  const router = useRouter();
  const urlSearchParams = useSearchParams();
  
  const [searchQuery, setSearchQuery] = useState(searchParams.query || '');
  const [selectedTags, setSelectedTags] = useState<string[]>(
    searchParams.tags ? searchParams.tags.split(',') : []
  );
  const [viewMode, setViewMode] = useState<'grid' | 'list'>('grid');
  const [sortBy, setSortBy] = useState(searchParams.sortBy || 'date');
  const [sortOrder, setSortOrder] = useState(searchParams.sortOrder || 'desc');
  const [showFilters, setShowFilters] = useState(false);

  const allTags = getAllTags();
  const featuredPosts = getFeaturedPosts();

  // Filter and sort posts
  const filteredPosts = useMemo(() => {
    let filtered = allBlogPosts.filter(post => post.published);

    // Apply search filter
    if (searchQuery) {
      const query = searchQuery.toLowerCase();
      filtered = filtered.filter(post =>
        post.title.toLowerCase().includes(query) ||
        post.excerpt.toLowerCase().includes(query) ||
        post.tags.some(tag => tag.toLowerCase().includes(query)) ||
        post.author.name.toLowerCase().includes(query)
      );
    }

    // Apply tag filter
    if (selectedTags.length > 0) {
      filtered = filtered.filter(post =>
        selectedTags.some(tag => post.tags.includes(tag))
      );
    }

    // Apply sorting
    filtered.sort((a, b) => {
      let comparison = 0;
      
      switch (sortBy) {
        case 'title':
          comparison = a.title.localeCompare(b.title);
          break;
        case 'readTime':
          comparison = a.readTime - b.readTime;
          break;
        case 'date':
        default:
          comparison = new Date(a.createdDate).getTime() - new Date(b.createdDate).getTime();
          break;
      }

      return sortOrder === 'desc' ? -comparison : comparison;
    });

    return filtered;
  }, [searchQuery, selectedTags, sortBy, sortOrder]);

  // Update URL when filters change
  useEffect(() => {
    const params = new URLSearchParams();
    
    if (searchQuery) params.set('query', searchQuery);
    if (selectedTags.length > 0) params.set('tags', selectedTags.join(','));
    if (sortBy !== 'date') params.set('sortBy', sortBy);
    if (sortOrder !== 'desc') params.set('sortOrder', sortOrder);

    const newUrl = params.toString() ? `/blog?${params.toString()}` : '/blog';
    router.replace(newUrl, { scroll: false });
  }, [searchQuery, selectedTags, sortBy, sortOrder, router]);

  const handleSearchSubmit = (e: React.FormEvent) => {
    e.preventDefault();
  };

  const clearFilters = () => {
    setSearchQuery('');
    setSelectedTags([]);
    setSortBy('date');
    setSortOrder('desc');
  };

  const hasActiveFilters = searchQuery || selectedTags.length > 0 || sortBy !== 'date' || sortOrder !== 'desc';

  return (
    <div className="min-h-screen">
      {/* Hero Section */}
      <BlogHero />

      {/* Main Content */}
      <div className="px-4 md:px-8 lg:px-16 xl:px-32 2xl:px-64 py-12">
        {/* Featured Posts Section */}
        {featuredPosts.length > 0 && !hasActiveFilters && (
          <section className="mb-16">
            <h2 className="text-3xl font-bold text-gray-900 mb-8">Featured Articles</h2>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
              {featuredPosts.map((post) => (
                <BlogCard key={post.id} post={post} featured />
              ))}
            </div>
          </section>
        )}

        {/* Search and Filter Section */}
        <div className="bg-white rounded-xl shadow-sm border p-6 mb-8">
          <div className="flex flex-col lg:flex-row gap-4 items-start lg:items-center justify-between">
            {/* Search Bar */}
            <form onSubmit={handleSearchSubmit} className="flex-1 max-w-md">
              <div className="relative">
                <input
                  type="text"
                  placeholder="Search articles..."
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                  className="w-full pl-10 pr-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-main-color focus:border-transparent transition-all"
                />
                <FiSearch className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-5 h-5" />
              </div>
            </form>

            {/* Controls */}
            <div className="flex items-center gap-4">
              {/* Filter Toggle */}
              <button
                onClick={() => setShowFilters(!showFilters)}
                className={`flex items-center gap-2 px-4 py-2 rounded-lg border transition-colors ${
                  showFilters || hasActiveFilters
                    ? 'bg-main-color text-white border-main-color'
                    : 'bg-white text-gray-700 border-gray-300 hover:bg-gray-50'
                }`}
              >
                <FiFilter className="w-4 h-4" />
                <span>Filters</span>
                {hasActiveFilters && (
                  <span className="bg-white text-main-color text-xs px-2 py-0.5 rounded-full">
                    {[searchQuery && 'Search', selectedTags.length && 'Tags', (sortBy !== 'date' || sortOrder !== 'desc') && 'Sort'].filter(Boolean).length}
                  </span>
                )}
              </button>

              {/* View Mode Toggle */}
              <div className="flex border border-gray-300 rounded-lg overflow-hidden">
                <button
                  onClick={() => setViewMode('grid')}
                  className={`p-2 transition-colors ${
                    viewMode === 'grid' ? 'bg-main-color text-white' : 'bg-white text-gray-700 hover:bg-gray-50'
                  }`}
                >
                  <FiGrid className="w-4 h-4" />
                </button>
                <button
                  onClick={() => setViewMode('list')}
                  className={`p-2 transition-colors ${
                    viewMode === 'list' ? 'bg-main-color text-white' : 'bg-white text-gray-700 hover:bg-gray-50'
                  }`}
                >
                  <FiList className="w-4 h-4" />
                </button>
              </div>
            </div>
          </div>

          {/* Expanded Filters */}
          {showFilters && (
            <div className="mt-6 pt-6 border-t border-gray-200">
              <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
                {/* Tags Filter */}
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-3">Filter by Tags</label>
                  <BlogTagFilter
                    tags={allTags}
                    selectedTags={selectedTags}
                    onTagsChange={setSelectedTags}
                  />
                </div>

                {/* Sort Options */}
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-3">Sort by</label>
                  <select
                    value={sortBy}
                    onChange={(e) => setSortBy(e.target.value)}
                    className="w-full p-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-main-color focus:border-transparent"
                  >
                    <option value="date">Date</option>
                    <option value="title">Title</option>
                    <option value="readTime">Read Time</option>
                  </select>
                </div>

                {/* Sort Order */}
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-3">Order</label>
                  <select
                    value={sortOrder}
                    onChange={(e) => setSortOrder(e.target.value)}
                    className="w-full p-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-main-color focus:border-transparent"
                  >
                    <option value="desc">Newest First</option>
                    <option value="asc">Oldest First</option>
                  </select>
                </div>
              </div>

              {/* Clear Filters */}
              {hasActiveFilters && (
                <div className="mt-4 flex justify-end">
                  <button
                    onClick={clearFilters}
                    className="flex items-center gap-2 px-4 py-2 text-gray-600 hover:text-gray-800 transition-colors"
                  >
                    <FiX className="w-4 h-4" />
                    Clear all filters
                  </button>
                </div>
              )}
            </div>
          )}
        </div>

        {/* Results Summary */}
        <div className="mb-6">
          <p className="text-gray-600">
            {filteredPosts.length === 0 ? 'No articles found' : 
             filteredPosts.length === 1 ? '1 article found' :
             `${filteredPosts.length} articles found`}
            {hasActiveFilters && (
              <button
                onClick={clearFilters}
                className="ml-2 text-main-color hover:underline"
              >
                Clear filters
              </button>
            )}
          </p>
        </div>

        {/* Blog Posts Grid/List */}
        {filteredPosts.length > 0 ? (
          <div className={
            viewMode === 'grid' 
              ? 'grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8'
              : 'space-y-8'
          }>
            {filteredPosts.map((post) => (
              <BlogCard 
                key={post.id} 
                post={post} 
                layout={viewMode}
              />
            ))}
          </div>
        ) : (
          <div className="text-center py-16">
            <div className="text-gray-400 text-6xl mb-4">📝</div>
            <h3 className="text-xl font-semibold text-gray-900 mb-2">No articles found</h3>
            <p className="text-gray-600 mb-6">
              Try adjusting your search terms or filters to find what you're looking for.
            </p>
            {hasActiveFilters && (
              <button
                onClick={clearFilters}
                className="bg-main-color text-white px-6 py-3 rounded-lg hover:bg-main-color/90 transition-colors"
              >
                Clear all filters
              </button>
            )}
          </div>
        )}
      </div>
    </div>
  );
}
