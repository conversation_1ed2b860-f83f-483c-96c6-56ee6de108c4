"use client";

import { useState } from 'react';
import { FiChevronDown, FiX } from 'react-icons/fi';

interface BlogTagFilterProps {
  tags: string[];
  selectedTags: string[];
  onTagsChange: (tags: string[]) => void;
  maxVisible?: number;
}

export default function BlogTagFilter({ 
  tags, 
  selectedTags, 
  onTagsChange, 
  maxVisible = 6 
}: BlogTagFilterProps) {
  const [showAll, setShowAll] = useState(false);

  const handleTagToggle = (tag: string) => {
    if (selectedTags.includes(tag)) {
      onTagsChange(selectedTags.filter(t => t !== tag));
    } else {
      onTagsChange([...selectedTags, tag]);
    }
  };

  const clearAllTags = () => {
    onTagsChange([]);
  };

  const visibleTags = showAll ? tags : tags.slice(0, maxVisible);
  const hasMoreTags = tags.length > maxVisible;

  return (
    <div className="space-y-3">
      {/* Selected Tags Summary */}
      {selectedTags.length > 0 && (
        <div className="flex flex-wrap items-center gap-2 p-3 bg-main-color/5 rounded-lg border border-main-color/20">
          <span className="text-sm font-medium text-gray-700">Selected:</span>
          {selectedTags.map((tag) => (
            <span
              key={tag}
              className="inline-flex items-center gap-1 bg-main-color text-white px-3 py-1 rounded-full text-sm"
            >
              {tag}
              <button
                onClick={() => handleTagToggle(tag)}
                className="hover:bg-white/20 rounded-full p-0.5 transition-colors"
              >
                <FiX className="w-3 h-3" />
              </button>
            </span>
          ))}
          <button
            onClick={clearAllTags}
            className="text-sm text-gray-500 hover:text-gray-700 transition-colors ml-2"
          >
            Clear all
          </button>
        </div>
      )}

      {/* Available Tags */}
      <div className="space-y-2">
        <div className="flex flex-wrap gap-2">
          {visibleTags.map((tag) => {
            const isSelected = selectedTags.includes(tag);
            return (
              <button
                key={tag}
                onClick={() => handleTagToggle(tag)}
                className={`px-3 py-2 rounded-lg text-sm font-medium transition-all duration-200 ${
                  isSelected
                    ? 'bg-main-color text-white shadow-sm'
                    : 'bg-gray-100 text-gray-700 hover:bg-gray-200 hover:shadow-sm'
                }`}
              >
                {tag}
                {isSelected && (
                  <span className="ml-2 text-white/80">✓</span>
                )}
              </button>
            );
          })}
        </div>

        {/* Show More/Less Button */}
        {hasMoreTags && (
          <button
            onClick={() => setShowAll(!showAll)}
            className="flex items-center gap-2 text-sm text-main-color hover:text-main-color/80 transition-colors font-medium"
          >
            {showAll ? 'Show less' : `Show ${tags.length - maxVisible} more`}
            <FiChevronDown 
              className={`w-4 h-4 transition-transform ${showAll ? 'rotate-180' : ''}`} 
            />
          </button>
        )}
      </div>

      {/* Tags Count */}
      <div className="text-xs text-gray-500">
        {selectedTags.length > 0 
          ? `${selectedTags.length} of ${tags.length} tags selected`
          : `${tags.length} tags available`
        }
      </div>
    </div>
  );
}
