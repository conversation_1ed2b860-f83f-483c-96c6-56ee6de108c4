"use client";

import { useState, useEffect } from "react";
import Image from "next/image";
import Link from "next/link";
import { Fi<PERSON>hopping<PERSON>ag, <PERSON>H<PERSON><PERSON>, FiEye } from "react-icons/fi";
import { useCartStore } from "@/hooks/useCartStore";

interface ProductGridProps {
  selectedCategory: number | null;
  selectedSubcategory: number | null;
}

import { fetchProductsByCategory } from "@/data/hierarchicalCategories";

// Product interface
interface Product {
  id: number;
  sku: string;
  name: string;
  slug: string;
  imageUrl: string;
  price: number;
  salePrice: number | null;
  inStock: boolean;
  created_at: string;
  subcategoryId?: number | null;
  rating?: number;
  reviewCount?: number;
  isNew?: boolean;
  isFeatured?: boolean;
  description?: string;
}

// Default products for initial render
const defaultProducts: Product[] = [];

const ProductGrid = ({ selectedCategory, selectedSubcategory }: ProductGridProps) => {
  const [hoveredProduct, setHoveredProduct] = useState<number | null>(null);
  const [products, setProducts] = useState<Product[]>(defaultProducts);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [page, setPage] = useState(1);
  const [totalPages, setTotalPages] = useState(1);
  const [addingToCartId, setAddingToCartId] = useState<number | null>(null);

  // Fetch products when category or subcategory changes
  useEffect(() => {
    const fetchProducts = async () => {
      setIsLoading(true);
      setError(null);

      try {
        if (selectedSubcategory) {
          // Fetch products for the selected subcategory
          const response = await fetchProductsByCategory(selectedSubcategory, page);
          setProducts(response.data);
          setTotalPages(Math.ceil(response.pagination.total / response.pagination.limit));
        } else if (selectedCategory) {
          // Fetch products for the selected category
          const response = await fetchProductsByCategory(selectedCategory, page);
          setProducts(response.data);
          setTotalPages(Math.ceil(response.pagination.total / response.pagination.limit));
        } else {
          // Fetch featured products or first page of all products
          // This would typically be a different API endpoint, but for now we'll use the same one
          setProducts(defaultProducts);
        }
      } catch (err) {
        setError('Failed to load products. Please try again.');
        console.error('Error fetching products:', err);
      } finally {
        setIsLoading(false);
      }
    };

    fetchProducts();
  }, [selectedCategory, selectedSubcategory, page]);

  // Loading state
  if (isLoading) {
    return (
      <div className="flex justify-center items-center h-64">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-main-color"></div>
      </div>
    );
  }

  // Error state
  if (error) {
    return (
      <div className="bg-red-50 border border-red-200 text-red-700 p-4 rounded-lg">
        <p>{error}</p>
        <button
          className="mt-2 bg-red-100 hover:bg-red-200 text-red-700 px-3 py-1 rounded text-sm"
          onClick={() => setPage(1)} // Reset and retry
        >
          Try Again
        </button>
      </div>
    );
  }

  // Empty state
  if (products.length === 0) {
    return (
      <div className="text-center py-12 bg-gray-50 rounded-lg">
        <p className="text-gray-500">No products found in this category.</p>
      </div>
    );
  }

  return (
    <div>
      <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-6">
        {products.map((product) => (
        <div
          key={product.id}
          className="group bg-white rounded-lg overflow-hidden shadow-sm border hover:shadow-md transition-all duration-300"
          onMouseEnter={() => setHoveredProduct(product.id)}
          onMouseLeave={() => setHoveredProduct(null)}
        >
          {/* Product Image */}
          <div className="relative h-64 overflow-hidden">
            <Link href={`/shop/${product.slug}`}>
              <Image
                src={product.imageUrl || 'https://via.placeholder.com/800x600?text=No+Image'}
                alt={product.name}
                fill
                className="object-cover transition-transform duration-500 group-hover:scale-105"
              />
            </Link>

            {/* Product Badges */}
            <div className="absolute top-3 left-3 flex flex-col gap-2">
              {product.isNew && (
                <span className="bg-green-500 text-white text-xs font-medium px-2 py-1 rounded">New</span>
              )}
              {product.salePrice && (
                <span className="bg-main-color text-white text-xs font-medium px-2 py-1 rounded">Sale</span>
              )}
            </div>

            {/* Quick Actions */}
            <div className="absolute top-3 right-3 flex flex-col gap-2">
              <button className="bg-white text-gray-700 hover:text-main-color p-2 rounded-full shadow-md opacity-0 group-hover:opacity-100 transition-opacity duration-300">
                <FiHeart size={18} />
              </button>
              <button className="bg-white text-gray-700 hover:text-main-color p-2 rounded-full shadow-md opacity-0 group-hover:opacity-100 transition-opacity duration-300">
                <FiEye size={18} />
              </button>
            </div>

            {/* Add to Cart Button */}
            <div className="absolute bottom-0 left-0 right-0 p-4 bg-gradient-to-t from-black/60 to-transparent translate-y-full group-hover:translate-y-0 transition-transform duration-300">
              <button
                className="w-full bg-main-color text-white py-2 rounded-lg flex items-center justify-center gap-2 hover:bg-main-color/90 transition-colors disabled:bg-gray-400"
                disabled={!product.inStock || addingToCartId === product.id}
                onClick={(e) => {
                  e.preventDefault(); // Prevent navigation
                  e.stopPropagation(); // Prevent event bubbling

                  if (!product.inStock) return;

                  setAddingToCartId(product.id);

                  const { addItem } = useCartStore.getState();
                  const productToAdd = {
                    id: product.id,
                    name: product.name,
                    price: product.salePrice !== null ? product.salePrice : product.price,
                    image: product.imageUrl || 'https://via.placeholder.com/800x600?text=No+Image',
                    sku: product.sku,
                    stockQuantity: 100, // Default value as we don't have exact quantity
                    stockStatus: product.inStock ? 'IN_STOCK' : 'OUT_OF_STOCK',
                    variantId: null
                  };

                  addItem(productToAdd, 1);

                  // Reset the loading state after a delay
                  setTimeout(() => {
                    setAddingToCartId(null);
                  }, 800);
                }}
              >
                <FiShoppingBag size={18} />
                <span>{addingToCartId === product.id ? 'Adding...' : 'Add to Cart'}</span>
              </button>
            </div>
          </div>

          {/* Product Info */}
          <div className="p-4">
            <Link href={`/shop/${product.slug}`} className="block">
              <h3 className="text-lg font-medium text-gray-800 hover:text-main-color transition-colors mb-1">
                {product.name}
              </h3>
            </Link>

            <div
              className="text-sm text-gray-600 mb-3 line-clamp-2 product-description"
              dangerouslySetInnerHTML={{
                __html: product.description || `${product.name} - ${product.sku}`
              }}
            />

            {/* Price */}
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-2">
                {product.salePrice ? (
                  <>
                    <span className="text-lg font-medium text-main-color">${product.salePrice}</span>
                    <span className="text-sm text-gray-500 line-through">${product.price}</span>
                  </>
                ) : (
                  <span className="text-lg font-medium text-gray-800">${product.price}</span>
                )}
              </div>

              {/* Stock Status */}
              <div className="text-xs font-medium px-2 py-1 rounded-full bg-gray-100">
                {product.inStock ? (
                  <span className="text-green-600">In Stock</span>
                ) : (
                  <span className="text-red-600">Out of Stock</span>
                )}
              </div>
            </div>
          </div>
        </div>
        ))}
      </div>

      {/* Pagination */}
      {totalPages > 1 && (
        <div className="mt-12 flex justify-center">
          <div className="flex items-center gap-2">
            <button
              onClick={() => setPage(Math.max(1, page - 1))}
              disabled={page === 1}
              className={`w-10 h-10 flex items-center justify-center rounded-lg border ${page === 1 ? 'border-gray-200 text-gray-400' : 'border-gray-300 text-gray-700 hover:bg-gray-100'}`}
            >
              &laquo;
            </button>

            {[...Array(totalPages)].map((_, i) => (
              <button
                key={i}
                onClick={() => setPage(i + 1)}
                className={`w-10 h-10 flex items-center justify-center rounded-lg ${i + 1 === page ? 'bg-main-color text-white' : 'border border-gray-300 text-gray-700 hover:bg-gray-100'}`}
              >
                {i + 1}
              </button>
            ))}

            <button
              onClick={() => setPage(Math.min(totalPages, page + 1))}
              disabled={page === totalPages}
              className={`w-10 h-10 flex items-center justify-center rounded-lg border ${page === totalPages ? 'border-gray-200 text-gray-400' : 'border-gray-300 text-gray-700 hover:bg-gray-100'}`}
            >
              &raquo;
            </button>
          </div>
        </div>
      )}
    </div>
  );
};

export default ProductGrid;
