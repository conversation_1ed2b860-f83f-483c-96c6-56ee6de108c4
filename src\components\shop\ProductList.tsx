"use client";

import { useState, useEffect } from "react";
import Image from "next/image";
import Link from "next/link";
import { FiShopping<PERSON><PERSON>, <PERSON>H<PERSON><PERSON>, FiEye } from "react-icons/fi";
import { fetchProductsByCategory } from "@/data/hierarchicalCategories";
import { useCartStore } from "@/hooks/useCartStore";

interface ProductListProps {
  selectedCategory: number | null;
  selectedSubcategory: number | null;
}

// Product interface
interface Product {
  id: number;
  sku: string;
  name: string;
  slug: string;
  imageUrl: string;
  price: number;
  salePrice: number | null;
  inStock: boolean;
  created_at: string;
  subcategoryId?: number | null;
  rating?: number;
  reviewCount?: number;
  isNew?: boolean;
  isFeatured?: boolean;
  description?: string;
}

// Default products for initial render
const defaultProducts: Product[] = [];

const ProductList = ({ selectedCategory, selectedSubcategory }: ProductListProps) => {
  const [products, setProducts] = useState<Product[]>(defaultProducts);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [page, setPage] = useState(1);
  const [totalPages, setTotalPages] = useState(1);
  const [addingToCartId, setAddingToCartId] = useState<number | null>(null);

  // Fetch products when category or subcategory changes
  useEffect(() => {
    const fetchProducts = async () => {
      setIsLoading(true);
      setError(null);

      try {
        if (selectedSubcategory) {
          // Fetch products for the selected subcategory
          const response = await fetchProductsByCategory(selectedSubcategory, page);
          setProducts(response.data);
          setTotalPages(Math.ceil(response.pagination.total / response.pagination.limit));
        } else if (selectedCategory) {
          // Fetch products for the selected category
          const response = await fetchProductsByCategory(selectedCategory, page);
          setProducts(response.data);
          setTotalPages(Math.ceil(response.pagination.total / response.pagination.limit));
        } else {
          // Fetch featured products or first page of all products
          // This would typically be a different API endpoint, but for now we'll use the same one
          setProducts(defaultProducts);
        }
      } catch (err) {
        setError('Failed to load products. Please try again.');
        console.error('Error fetching products:', err);
      } finally {
        setIsLoading(false);
      }
    };

    fetchProducts();
  }, [selectedCategory, selectedSubcategory, page]);

  // Loading state
  if (isLoading) {
    return (
      <div className="flex justify-center items-center h-64">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-main-color"></div>
      </div>
    );
  }

  // Error state
  if (error) {
    return (
      <div className="bg-red-50 border border-red-200 text-red-700 p-4 rounded-lg">
        <p>{error}</p>
        <button
          className="mt-2 bg-red-100 hover:bg-red-200 text-red-700 px-3 py-1 rounded text-sm"
          onClick={() => setPage(1)} // Reset and retry
        >
          Try Again
        </button>
      </div>
    );
  }

  // Empty state
  if (products.length === 0) {
    return (
      <div className="text-center py-12 bg-gray-50 rounded-lg">
        <p className="text-gray-500">No products found in this category.</p>
      </div>
    );
  }

  return (
    <div>
      <div className="space-y-6">
        {products.map((product) => (
        <div
          key={product.id}
          className="group bg-white rounded-lg overflow-hidden shadow-sm border hover:shadow-md transition-all duration-300 flex flex-col md:flex-row"
        >
          {/* Product Image */}
          <div className="relative h-64 md:h-auto md:w-1/3 md:min-h-[200px] overflow-hidden">
            <Link href={`/shop/${product.slug}`}>
              <Image
                src={product.imageUrl || 'https://via.placeholder.com/800x600?text=No+Image'}
                alt={product.name}
                fill
                className="object-cover transition-transform duration-500 group-hover:scale-105"
              />
            </Link>

            {/* Product Badges */}
            <div className="absolute top-3 left-3 flex flex-col gap-2">
              {product.isNew && (
                <span className="bg-green-500 text-white text-xs font-medium px-2 py-1 rounded">New</span>
              )}
              {product.salePrice && (
                <span className="bg-main-color text-white text-xs font-medium px-2 py-1 rounded">Sale</span>
              )}
            </div>
          </div>

          {/* Product Info */}
          <div className="p-6 flex-1 flex flex-col">
            <div className="flex-1">
              <Link href={`/shop/${product.slug}`} className="block">
                <h3 className="text-xl font-medium text-gray-800 hover:text-main-color transition-colors mb-2">
                  {product.name}
                </h3>
              </Link>

              {/* SKU */}
              <div className="text-sm text-gray-500 mb-3">
                SKU: {product.sku}
              </div>

              <div
                className="text-gray-600 mb-4 product-description"
                dangerouslySetInnerHTML={{
                  __html: product.description || `${product.name} - ${product.sku}`
                }}
              />

              {/* Stock Status */}
              <div className="mb-4">
                {product.inStock ? (
                  <span className="text-sm text-green-600 font-medium">In Stock</span>
                ) : (
                  <span className="text-sm text-red-500 font-medium">Out of Stock</span>
                )}
              </div>
            </div>

            <div className="flex flex-col sm:flex-row items-start sm:items-center justify-between gap-4 mt-4 pt-4 border-t">
              {/* Price */}
              <div className="flex items-center gap-2">
                {product.salePrice ? (
                  <>
                    <span className="text-2xl font-medium text-main-color">${product.salePrice}</span>
                    <span className="text-lg text-gray-500 line-through">${product.price}</span>
                  </>
                ) : (
                  <span className="text-2xl font-medium text-gray-800">${product.price}</span>
                )}
              </div>

              {/* Actions */}
              <div className="flex items-center gap-2">
                <button className="bg-white border border-gray-300 text-gray-700 hover:text-main-color hover:border-main-color p-2 rounded-full transition-colors">
                  <FiHeart size={18} />
                </button>
                <button className="bg-white border border-gray-300 text-gray-700 hover:text-main-color hover:border-main-color p-2 rounded-full transition-colors">
                  <FiEye size={18} />
                </button>
                <button
                  className="bg-main-color text-white px-4 py-2 rounded-lg flex items-center gap-2 hover:bg-main-color/90 transition-colors disabled:bg-gray-400"
                  disabled={!product.inStock || addingToCartId === product.id}
                  onClick={(e) => {
                    e.preventDefault(); // Prevent navigation
                    e.stopPropagation(); // Prevent event bubbling

                    if (!product.inStock) return;

                    setAddingToCartId(product.id);

                    const { addItem } = useCartStore.getState();
                    const productToAdd = {
                      id: product.id,
                      name: product.name,
                      price: product.salePrice !== null ? product.salePrice : product.price,
                      image: product.imageUrl || 'https://via.placeholder.com/800x600?text=No+Image',
                      sku: product.sku,
                      stockQuantity: 100, // Default value as we don't have exact quantity
                      stockStatus: product.inStock ? 'IN_STOCK' : 'OUT_OF_STOCK',
                      variantId: null
                    };

                    addItem(productToAdd, 1);

                    // Reset the loading state after a delay
                    setTimeout(() => {
                      setAddingToCartId(null);
                    }, 800);
                  }}
                >
                  <FiShoppingBag size={18} />
                  <span>{addingToCartId === product.id ? 'Adding...' : 'Add to Cart'}</span>
                </button>
              </div>
            </div>
          </div>
        </div>
        ))}
      </div>

      {/* Pagination */}
      {totalPages > 1 && (
        <div className="mt-12 flex justify-center">
          <div className="flex items-center gap-2">
            <button
              onClick={() => setPage(Math.max(1, page - 1))}
              disabled={page === 1}
              className={`w-10 h-10 flex items-center justify-center rounded-lg border ${page === 1 ? 'border-gray-200 text-gray-400' : 'border-gray-300 text-gray-700 hover:bg-gray-100'}`}
            >
              &laquo;
            </button>

            {[...Array(totalPages)].map((_, i) => (
              <button
                key={i}
                onClick={() => setPage(i + 1)}
                className={`w-10 h-10 flex items-center justify-center rounded-lg ${i + 1 === page ? 'bg-main-color text-white' : 'border border-gray-300 text-gray-700 hover:bg-gray-100'}`}
              >
                {i + 1}
              </button>
            ))}

            <button
              onClick={() => setPage(Math.min(totalPages, page + 1))}
              disabled={page === totalPages}
              className={`w-10 h-10 flex items-center justify-center rounded-lg border ${page === totalPages ? 'border-gray-200 text-gray-400' : 'border-gray-300 text-gray-700 hover:bg-gray-100'}`}
            >
              &raquo;
            </button>
          </div>
        </div>
      )}
    </div>
  );
};

export default ProductList;
