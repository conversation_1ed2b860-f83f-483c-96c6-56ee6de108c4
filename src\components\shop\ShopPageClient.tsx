"use client";

import { useState } from "react";
import Image from "next/image";
import Link from "next/link";
import { FiShoppingBag } from "react-icons/fi";
import { useCartStore } from "@/hooks/useCartStore";
import { createShopUrl, ensureValidSlug } from "@/utils/slugUtils";

// Types
interface Product {
  id: number;
  sku: string;
  name: string;
  slug: string;
  imageUrl: string;
  price: number;
  salePrice?: number;
  inStock: boolean;
  created_at: string;
}

interface MainCategory {
  id: number;
  name: string;
  slug: string;
  imageUrl: string;
  count: number;
}

interface ProductsData {
  pagination: {
    total: number;
    page: number;
    limit: number;
  };
  data: Product[];
}

export interface ShopPageClientProps {
  initialProducts: ProductsData;
  mainCategories: MainCategory[];
}

const ShopPageClient = ({ initialProducts, mainCategories }: ShopPageClientProps) => {
  const [addingToCartId, setAddingToCartId] = useState<number | null>(null);
  const { addItem } = useCartStore();

  const handleAddToCart = async (product: Product) => {
    setAddingToCartId(product.id);

    try {
      await addItem({
        id: product.id.toString(),
        name: product.name,
        price: product.salePrice || product.price,
        image: product.imageUrl,
        sku: product.sku || `PROD-${product.id}`,
        stockQuantity: 100, // Default value
        stockStatus: product.inStock ? 'IN_STOCK' : 'OUT_OF_STOCK'
      }, 1);
    } catch (error) {
      console.error('Error adding to cart:', error);
    } finally {
      setAddingToCartId(null);
    }
  };

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Hero Section with Background Image */}
      <div
        className="relative h-96 bg-cover bg-center bg-no-repeat"
        style={{
          backgroundImage: "url('https://images.unsplash.com/photo-1596462502278-27bfdc403348?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=2340&q=80')"
        }}
      >
        <div className="absolute inset-0 bg-black bg-opacity-40"></div>
        <div className="relative z-10 flex items-center justify-center h-full">
          <div className="text-center text-white">
            <h1 className="text-5xl font-bold mb-4">Shop Our Collection</h1>
            <p className="text-xl max-w-2xl mx-auto">
              Discover premium organic beauty products crafted with pure, natural ingredients for real results and well-being.
            </p>
          </div>
        </div>
      </div>

      {/* Main Content */}
      <div className="px-4 md:px-8 lg:px-16 xl:px-32 2xl:px-64 py-12">
        {/* Main Categories Section */}
        <section className="mb-16">
          <div className="text-center mb-12">
            <h2 className="text-3xl font-bold text-gray-800 mb-4">Shop by Category</h2>
            <p className="text-gray-600 max-w-2xl mx-auto">
              Explore our carefully curated categories of premium organic beauty products.
            </p>
          </div>

          <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-6">
            {mainCategories.map((category) => (
              <Link
                key={category.id}
                href={createShopUrl(ensureValidSlug(category.slug))}
                className="group relative overflow-hidden rounded-lg shadow-md hover:shadow-lg transition-all duration-300"
              >
                <div className="relative h-64 w-full overflow-hidden">
                  <Image
                    src={category.imageUrl}
                    alt={category.name}
                    fill
                    className="object-cover transition-transform duration-500 group-hover:scale-110"
                  />
                  <div className="absolute inset-0 bg-gradient-to-t from-black/70 to-transparent"></div>
                </div>
                <div className="absolute bottom-0 left-0 right-0 p-4 text-white">
                  <h3 className="text-xl font-medium">{category.name}</h3>
                  <p className="text-sm opacity-80">{category.count} products</p>
                </div>
                <div className="absolute inset-0 flex items-center justify-center bg-main-color/0 group-hover:bg-main-color/20 transition-all duration-300">
                  <button className="bg-white text-main-color px-4 py-2 rounded-full font-medium transform translate-y-10 opacity-0 group-hover:translate-y-0 group-hover:opacity-100 transition-all duration-300">
                    View Category
                  </button>
                </div>
              </Link>
            ))}
          </div>
        </section>

        {/* Featured Products Section */}
        <section className="mb-16">
          <div className="text-center mb-12">
            <h2 className="text-3xl font-bold text-gray-800 mb-4">Featured Products</h2>
            <p className="text-gray-600 max-w-2xl mx-auto">
              Discover our most popular and highly-rated products from across all categories.
            </p>
          </div>

          {/* Products Grid */}
          <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
            {initialProducts.data.map((product) => (
              <div key={product.id} className="group bg-white rounded-lg shadow-sm hover:shadow-md transition-shadow duration-300">
                <div className="relative h-64 overflow-hidden rounded-t-lg">
                  <Link href={`/product/${ensureValidSlug(product.slug)}`}>
                    <Image
                      src={product.imageUrl}
                      alt={product.name}
                      fill
                      className="object-cover transition-transform duration-300 group-hover:scale-105"
                    />
                  </Link>
                  {product.salePrice && (
                    <div className="absolute top-2 left-2 bg-red-500 text-white px-2 py-1 rounded text-sm font-medium">
                      Sale
                    </div>
                  )}
                  {!product.inStock && (
                    <div className="absolute top-2 right-2 bg-gray-500 text-white px-2 py-1 rounded text-sm font-medium">
                      Out of Stock
                    </div>
                  )}
                </div>

                <div className="p-4">
                  <div>
                    <Link href={`/product/${ensureValidSlug(product.slug)}`}>
                      <h3 className="font-medium text-gray-800 mb-2 line-clamp-2 hover:text-main-color transition-colors">
                        {product.name}
                      </h3>
                    </Link>

                    <div className="flex items-center justify-between">
                      <div className="flex items-center space-x-2">
                        {product.salePrice ? (
                          <>
                            <span className="text-lg font-bold text-main-color">
                              ${product.salePrice.toFixed(2)}
                            </span>
                            <span className="text-sm text-gray-500 line-through">
                              ${product.price.toFixed(2)}
                            </span>
                          </>
                        ) : (
                          <span className="text-lg font-bold text-gray-800">
                            ${product.price.toFixed(2)}
                          </span>
                        )}
                      </div>

                      <button
                        onClick={() => handleAddToCart(product)}
                        disabled={!product.inStock || addingToCartId === product.id}
                        className={`p-2 rounded-full transition-colors ${
                          product.inStock
                            ? "bg-main-color text-white hover:bg-main-color/90"
                            : "bg-gray-300 text-gray-500 cursor-not-allowed"
                        }`}
                      >
                        {addingToCartId === product.id ? (
                          <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white"></div>
                        ) : (
                          <FiShoppingBag size={16} />
                        )}
                      </button>
                    </div>
                  </div>
                </div>
              </div>
            ))}
          </div>

          {/* Pagination Info */}
          {initialProducts.pagination.total > initialProducts.data.length && (
            <div className="text-center mt-8">
              <p className="text-gray-600 mb-4">
                Showing {initialProducts.data.length} of {initialProducts.pagination.total} products
              </p>
              <Link
                href="/shop?page=2"
                className="inline-flex items-center px-6 py-3 bg-main-color text-white rounded-lg hover:bg-main-color/90 transition-colors"
              >
                Load More Products
              </Link>
            </div>
          )}
        </section>
      </div>
    </div>
  );
};

export default ShopPageClient;
