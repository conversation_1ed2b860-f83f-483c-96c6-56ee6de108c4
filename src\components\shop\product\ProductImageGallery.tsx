"use client";

import { useState, useEffect } from 'react';
import Image from 'next/image';
import { FiChevronLeft, FiChevronRight, FiZoomIn } from 'react-icons/fi';

interface ProductImage {
  id: number;
  url: string;
  position: number;
}

interface ProductImageGalleryProps {
  images: ProductImage[];
}

const ProductImageGallery = ({ images }: ProductImageGalleryProps) => {
  const [currentImageIndex, setCurrentImageIndex] = useState(0);
  const [isZoomed, setIsZoomed] = useState(false);
  const [zoomPosition, setZoomPosition] = useState({ x: 0, y: 0 });

  // Reset current image index when images change
  useEffect(() => {
    setCurrentImageIndex(0);
    setIsZoomed(false);
  }, [images]);

  // Handle navigation to previous image
  const handlePrevImage = () => {
    if (!images || images.length === 0) return;
    setCurrentImageIndex((prev) => (prev === 0 ? images.length - 1 : prev - 1));
    setIsZoomed(false);
  };

  // Handle navigation to next image
  const handleNextImage = () => {
    if (!images || images.length === 0) return;
    setCurrentImageIndex((prev) => (prev === images.length - 1 ? 0 : prev + 1));
    setIsZoomed(false);
  };

  // Handle thumbnail click
  const handleThumbnailClick = (index: number) => {
    setCurrentImageIndex(index);
    setIsZoomed(false);
  };

  // Handle zoom toggle
  const handleZoomToggle = () => {
    setIsZoomed(!isZoomed);
  };

  // Handle mouse move for zoom effect
  const handleMouseMove = (e: React.MouseEvent<HTMLDivElement>) => {
    if (!isZoomed) return;

    const { left, top, width, height } = e.currentTarget.getBoundingClientRect();
    const x = ((e.clientX - left) / width) * 100;
    const y = ((e.clientY - top) / height) * 100;

    setZoomPosition({ x, y });
  };

  // Debug image data
  useEffect(() => {
    console.log('ProductImageGallery - Images received:', images);

    // Add cache-busting parameter to loremflickr URLs to prevent caching issues
    if (images && images.length > 0) {
      console.log('ProductImageGallery - Image URLs:', images.map(img => img.url));
      const hasLoremFlickr = images.some(img => img.url.includes('loremflickr.com'));
      if (hasLoremFlickr) {
        console.log('ProductImageGallery - Detected loremflickr images, adding cache-busting');
      }
    } else {
      console.log('ProductImageGallery - No images or empty array received');
    }
  }, [images]);

  // If no images, show placeholder
  if (!images || images.length === 0) {
    console.log('ProductImageGallery - No images available');
    return (
      <div className="w-full aspect-square bg-gray-100 rounded-lg flex items-center justify-center">
        <span className="text-gray-400">No image available</span>
      </div>
    );
  }

  // Sort images by position and process URLs
  const sortedImages = [...images].map(img => {
    // Add a timestamp to loremflickr URLs to prevent caching issues
    if (img.url.includes('loremflickr.com')) {
      const timestamp = Date.now();
      const separator = img.url.includes('?') ? '&' : '?';
      return {
        ...img,
        url: `${img.url}${separator}t=${timestamp}`
      };
    }
    return img;
  }).sort((a, b) => a.position - b.position);

  console.log('ProductImageGallery - Processed images:', sortedImages);

  // Make sure currentImageIndex is valid
  const validIndex = Math.min(currentImageIndex, sortedImages.length - 1);
  const currentImage = sortedImages[validIndex];

  // Log the current image for debugging
  console.log('ProductImageGallery - Current image:', currentImage);
  console.log('ProductImageGallery - Current image URL:', currentImage.url);

  return (
    <div className="w-full">
      {/* Main Image */}
      <div className="relative w-full h-[400px] mb-4 rounded-lg overflow-hidden bg-white border border-gray-200">
        <div
          className={`relative w-full h-full cursor-${isZoomed ? 'zoom-out' : 'zoom-in'}`}
          onClick={handleZoomToggle}
          onMouseMove={handleMouseMove}
          onMouseLeave={() => isZoomed && setIsZoomed(false)}
        >
          {/* Fallback image as regular img tag */}
          <img
            src={currentImage.url}
            alt="Product image (fallback)"
            className="absolute inset-0 w-full h-full object-contain z-0"
          />

          {/* Next.js Image component */}
          <Image
            src={currentImage.url}
            alt="Product image"
            fill
            className={`object-contain transition-transform duration-300 ${
              isZoomed ? 'scale-150' : 'scale-100'
            } z-10`}
            style={
              isZoomed
                ? {
                    transformOrigin: `${zoomPosition.x}% ${zoomPosition.y}%`,
                  }
                : undefined
            }
            sizes="(max-width: 768px) 100vw, 50vw"
            priority
            // Use unoptimized for all images to avoid optimization issues
            unoptimized={true}
            // Disable caching for all images
            loading="eager"
            onLoad={() => {
              console.log('Image loaded successfully:', currentImage.url);
            }}
            onError={(e) => {
              console.error('Image failed to load:', currentImage.url);
              // Fallback to a placeholder if the image fails to load
              (e.target as HTMLImageElement).src = 'https://via.placeholder.com/400?text=Image+Not+Available';
            }}
          />
        </div>

        {/* Zoom indicator */}
        <div className="absolute top-4 right-4 bg-white/80 p-2 rounded-full shadow-md">
          <FiZoomIn size={18} className="text-gray-700" />
        </div>

        {/* Navigation arrows */}
        {sortedImages.length > 1 && (
          <>
            <button
              onClick={handlePrevImage}
              className="absolute left-4 top-1/2 -translate-y-1/2 bg-white/80 p-2 rounded-full shadow-md hover:bg-white transition-colors"
              aria-label="Previous image"
            >
              <FiChevronLeft size={20} className="text-gray-700" />
            </button>
            <button
              onClick={handleNextImage}
              className="absolute right-4 top-1/2 -translate-y-1/2 bg-white/80 p-2 rounded-full shadow-md hover:bg-white transition-colors"
              aria-label="Next image"
            >
              <FiChevronRight size={20} className="text-gray-700" />
            </button>
          </>
        )}
      </div>

      {/* Thumbnails */}
      {sortedImages.length > 1 && (
        <div className="flex gap-2 overflow-x-auto pb-2 scrollbar-hide">
          {sortedImages.map((image, index) => (
            <button
              key={image.id}
              onClick={() => handleThumbnailClick(index)}
              className={`relative w-20 h-20 flex-shrink-0 rounded-md overflow-hidden border-2 transition-colors ${
                index === currentImageIndex ? 'border-main-color' : 'border-transparent hover:border-gray-300'
              }`}
              aria-label={`View image ${index + 1}`}
            >
              {/* Fallback thumbnail as regular img tag */}
              <img
                src={image.url}
                alt={`Product thumbnail ${index + 1} (fallback)`}
                className="absolute inset-0 w-full h-full object-cover z-0"
              />

              {/* Next.js Image component */}
              <Image
                src={image.url}
                alt={`Product thumbnail ${index + 1}`}
                fill
                className="object-cover z-10"
                sizes="80px"
                // Use unoptimized for all images to avoid optimization issues
                unoptimized={true}
                // Disable caching for all images
                loading="eager"
                onLoad={() => {
                  console.log('Thumbnail loaded successfully:', image.url);
                }}
                onError={(e) => {
                  console.error('Thumbnail failed to load:', image.url);
                  // Fallback to a placeholder if the thumbnail fails to load
                  (e.target as HTMLImageElement).src = 'https://via.placeholder.com/80?text=No+Image';
                }}
              />
            </button>
          ))}
        </div>
      )}
    </div>
  );
};

export default ProductImageGallery;
