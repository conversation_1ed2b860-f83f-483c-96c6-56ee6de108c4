"use client";

import { useState } from 'react';
import { FiList, FiChevronDown, FiChevronUp } from 'react-icons/fi';

interface ProductListing {
  id: number;
  title: string;
  content: string;
}

interface ProductListingsProps {
  listings: ProductListing[];
}

const ProductListings = ({ listings }: ProductListingsProps) => {
  const [expandedListings, setExpandedListings] = useState<Record<number, boolean>>({});

  // Toggle a listing's expanded state
  const toggleListing = (listingId: number) => {
    setExpandedListings(prev => ({
      ...prev,
      [listingId]: !prev[listingId]
    }));
  };

  // Check if a listing is expanded
  const isListingExpanded = (listingId: number) => {
    return !!expandedListings[listingId];
  };

  return (
    <div>
      <h2 className="text-2xl font-medium text-gray-800 mb-6 flex items-center gap-2">
        <FiList size={20} /> Additional Information
      </h2>
      
      <div className="space-y-4">
        {listings.map((listing) => (
          <div 
            key={listing.id} 
            className="border border-gray-200 rounded-lg overflow-hidden"
          >
            {/* Listing Header */}
            <button
              onClick={() => toggleListing(listing.id)}
              className="w-full flex items-center justify-between p-4 bg-gray-50 hover:bg-gray-100 transition-colors text-left"
            >
              <h3 className="font-medium text-gray-800">{listing.title}</h3>
              {isListingExpanded(listing.id) ? (
                <FiChevronUp size={20} className="text-gray-600" />
              ) : (
                <FiChevronDown size={20} className="text-gray-600" />
              )}
            </button>
            
            {/* Listing Content */}
            {isListingExpanded(listing.id) && (
              <div className="p-4 bg-white">
                <div 
                  className="prose max-w-none text-gray-700"
                  dangerouslySetInnerHTML={{ __html: listing.content }}
                />
              </div>
            )}
          </div>
        ))}
      </div>
    </div>
  );
};

export default ProductListings;
