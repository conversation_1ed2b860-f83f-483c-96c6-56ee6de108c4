"use client";

import { useState } from 'react';
import Image from 'next/image';
import { FiStar, FiUser, FiCalendar, FiImage } from 'react-icons/fi';

interface ReviewMedia {
  id: string;
  url: string;
}

interface ReviewCustomer {
  display_name: string;
  avatar_url: string;
}

interface Review {
  id: string;
  customer: ReviewCustomer;
  rating: number;
  heading?: string;
  body: string;
  media?: ReviewMedia[];
  created_at?: string;
}

interface ProductReviewsProps {
  productId: number;
}

const ProductReviews = ({ productId }: ProductReviewsProps) => {
  const [expandedImageUrl, setExpandedImageUrl] = useState<string | null>(null);

  // Dummy reviews data - in a real app, this would be fetched from an API
  const dummyReviews: Review[] = [
    {
      id: "1",
      customer: {
        display_name: "<PERSON>",
        avatar_url: "https://images.unsplash.com/photo-1599566150163-29194dcaad36?w=800&auto=format&fit=crop"
      },
      rating: 5,
      heading: "Amazing natural product",
      body: "I've been using this for a month now and I can really see the difference. My skin feels so much better! The ingredients are all natural and it has a lovely scent that isn't overwhelming. Highly recommend to anyone looking for a quality skincare product.",
      media: [
        {
          id: "m1",
          url: "https://images.unsplash.com/photo-1556229010-6c3f2c9ca5f8?w=800&auto=format&fit=crop"
        }
      ],
      created_at: "2023-10-15T10:30:00Z"
    },
    {
      id: "2",
      customer: {
        display_name: "Sarah Smith",
        avatar_url: "https://images.unsplash.com/photo-1494790108377-be9c29b29330?w=800&auto=format&fit=crop"
      },
      rating: 4,
      heading: "Great quality",
      body: "The ingredients are all natural and the scent is lovely. Would recommend! I've been using it for a couple of weeks and have noticed my skin is more hydrated.",
      media: [],
      created_at: "2023-09-22T14:45:00Z"
    },
    {
      id: "3",
      customer: {
        display_name: "Mike Johnson",
        avatar_url: "https://images.unsplash.com/photo-1552374196-c4e7ffc6e126?w=800&auto=format&fit=crop"
      },
      rating: 5,
      heading: "Worth every penny",
      body: "Excellent product, fast shipping, and great customer service. Will buy again! The packaging is also eco-friendly which I really appreciate.",
      media: [
        {
          id: "m2",
          url: "https://images.unsplash.com/photo-1617897903246-719242758050?w=800&auto=format&fit=crop"
        },
        {
          id: "m3",
          url: "https://images.unsplash.com/photo-1598662972299-5408ddb8a3dc?w=800&auto=format&fit=crop"
        }
      ],
      created_at: "2023-11-05T09:15:00Z"
    }
  ];

  // Format date to a readable string
  const formatDate = (dateString?: string) => {
    if (!dateString) return '';

    const date = new Date(dateString);
    return date.toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    });
  };

  // Render star rating
  const renderStarRating = (rating: number) => {
    return (
      <div className="flex items-center gap-1">
        {[1, 2, 3, 4, 5].map((star) => (
          <FiStar
            key={star}
            size={16}
            className={`${
              star <= rating ? 'text-yellow-400 fill-yellow-400' : 'text-gray-300'
            }`}
          />
        ))}
      </div>
    );
  };

  // Handle image click to expand
  const handleImageClick = (url: string) => {
    setExpandedImageUrl(url);
  };

  // Close expanded image
  const closeExpandedImage = () => {
    setExpandedImageUrl(null);
  };

  return (
    <div>
      {dummyReviews.length === 0 ? (
        <div className="text-center py-12 bg-gray-50 rounded-lg">
          <p className="text-gray-600">No reviews yet. Be the first to review this product!</p>
        </div>
      ) : (
        <div className="space-y-10">
          {dummyReviews.map((review) => (
            <div key={review.id} className="border-b border-gray-200 pb-10">
              {/* Review Header */}
              <div className="flex items-start justify-between mb-4">
                {/* Customer Info */}
                <div>
                  <h3 className="font-medium text-gray-800">{review.customer.display_name}</h3>
                  {review.created_at && (
                    <div className="flex items-center gap-1 text-sm text-gray-500">
                      <FiCalendar size={14} />
                      <span>{formatDate(review.created_at)}</span>
                    </div>
                  )}
                </div>

                {/* Rating */}
                <div>
                  {renderStarRating(review.rating)}
                </div>
              </div>

              {/* Review Content */}
              <div className="mb-4">
                {review.heading && (
                  <h4 className="font-medium text-gray-800 mb-2">{review.heading}</h4>
                )}
                <p className="text-gray-600 whitespace-pre-line">{review.body}</p>
              </div>

              {/* Review Media */}
              {review.media && review.media.length > 0 && (
                <div className="mt-4">
                  <h5 className="text-sm font-medium text-gray-700 mb-2 flex items-center gap-1">
                    <FiImage size={16} />
                    <span>Photos from this review</span>
                  </h5>
                  <div className="flex flex-wrap gap-2">
                    {review.media.map((media) => (
                      <button
                        key={media.id}
                        onClick={() => handleImageClick(media.url)}
                        className="relative w-20 h-20 rounded-md overflow-hidden border border-gray-200 hover:border-main-color transition-colors"
                      >
                        <Image
                          src={media.url}
                          alt="Review image"
                          fill
                          className="object-cover"
                          sizes="80px"
                        />
                      </button>
                    ))}
                  </div>
                </div>
              )}
            </div>
          ))}
        </div>
      )}

      {/* Expanded Image Modal */}
      {expandedImageUrl && (
        <div
          className="fixed inset-0 bg-black/80 z-50 flex items-center justify-center p-4"
          onClick={closeExpandedImage}
        >
          <div className="relative max-w-4xl max-h-[90vh] w-full h-full">
            <Image
              src={expandedImageUrl}
              alt="Expanded review image"
              fill
              className="object-contain"
              sizes="(max-width: 768px) 100vw, 80vw"
            />
          </div>
          <button
            className="absolute top-4 right-4 bg-white/20 text-white p-2 rounded-full hover:bg-white/30 transition-colors"
            onClick={closeExpandedImage}
          >
            <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
              <line x1="18" y1="6" x2="6" y2="18"></line>
              <line x1="6" y1="6" x2="18" y2="18"></line>
            </svg>
          </button>
        </div>
      )}
    </div>
  );
};

export default ProductReviews;
