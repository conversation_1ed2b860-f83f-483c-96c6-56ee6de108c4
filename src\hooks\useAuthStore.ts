import { create } from "zustand";
import { authApi, User, LoginRequest, RegisterRequest, CreateAdminRequest } from "@/services/authApi";

interface AuthState {
  user: User | null;
  token: string | null;
  isLoading: boolean;
  error: string | null;

  // Actions
  register: (email: string, password: string, name?: string) => Promise<User | null>;
  login: (email: string, password: string) => Promise<User | null>;
  logout: () => Promise<void>;
  getProfile: () => Promise<User | null>;
  createAdmin: (adminData: CreateAdminRequest) => Promise<User | null>;
  clearError: () => void;
  isAuthenticated: () => boolean;
}

// Helper to get token from localStorage
const getStoredToken = (): string | null => {
  if (typeof window === 'undefined') return null;
  return localStorage.getItem('accessToken');
};

// Helper to remove token from localStorage
const removeToken = (): void => {
  if (typeof window === 'undefined') return;
  localStorage.removeItem('accessToken');
};

export const useAuthStore = create<AuthState>((set, get) => ({
  user: null,
  token: getStoredToken(),
  isLoading: false,
  error: null,

  clearError: () => set({ error: null }),

  isAuthenticated: () => {
    return !!get().token;
  },

  register: async (email: string, password: string, name?: string) => {
    set({ isLoading: true, error: null });
    try {
      const registerData: RegisterRequest = { email, password };
      if (name) registerData.name = name;

      // Use the public registration endpoint
      const data = await authApi.registerUser(registerData);

      set({
        user: data,
        token: data.accessToken || null,
        isLoading: false
      });

      return data;
    } catch (error) {
      console.error('Auth store error - register:', error);
      set({
        error: error instanceof Error ? error.message : 'Registration failed',
        isLoading: false,
      });
      return null;
    }
  },

  login: async (email: string, password: string) => {
    set({ isLoading: true, error: null });
    try {
      const data = await authApi.login({ email, password });

      set({
        user: data,
        token: data.accessToken || null,
        isLoading: false
      });

      return data;
    } catch (error) {
      console.error('Auth store error - login:', error);
      set({
        error: error instanceof Error ? error.message : 'Login failed',
        isLoading: false,
      });
      return null;
    }
  },

  logout: async () => {
    set({ isLoading: true });
    try {
      await authApi.logout();

      // Clear token and user data
      set({ user: null, token: null, isLoading: false });
    } catch (error) {
      console.error('Auth store error - logout:', error);
      // Still clear the token and user data on error
      set({ user: null, token: null, isLoading: false });
    }
  },

  getProfile: async () => {
    if (!getStoredToken()) return null;

    set({ isLoading: true, error: null });
    try {
      const data = await authApi.getProfile();

      set({
        user: data,
        isLoading: false
      });

      return data;
    } catch (error) {
      console.error('Auth store error - getProfile:', error);
      set({
        error: error instanceof Error ? error.message : 'Failed to fetch profile',
        isLoading: false,
      });
      return null;
    }
  },

  createAdmin: async (adminData: CreateAdminRequest) => {
    set({ isLoading: true, error: null });
    try {
      const data = await authApi.createAdmin(adminData);

      set({ isLoading: false });

      return data;
    } catch (error) {
      console.error('Auth store error - createAdmin:', error);
      set({
        error: error instanceof Error ? error.message : 'Failed to create admin',
        isLoading: false,
      });
      return null;
    }
  },
}));
