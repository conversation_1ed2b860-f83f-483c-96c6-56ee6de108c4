export interface BlogContentBlock {
  type: 'text' | 'image' | 'heading' | 'list' | 'quote';
  content: string;
  imageUrl?: string;
  imageAlt?: string;
  level?: number; // for headings (h1, h2, h3, etc.)
  listItems?: string[]; // for lists
}

export interface BlogPost {
  id: string;
  title: string;
  slug: string;
  tags: string[];
  content: BlogContentBlock[]; // JSON structure for mixed content
  blogImage: string;
  author: {
    name: string;
    avatar?: string;
    bio?: string;
  };
  createdDate: string;
  updatedDate?: string;
  excerpt: string;
  readTime: number; // in minutes
  featured: boolean;
  published: boolean;
  seo: {
    metaTitle?: string;
    metaDescription?: string;
    keywords?: string[];
  };
}

export interface BlogSearchParams {
  query?: string;
  tags?: string[];
  page?: number;
  limit?: number;
  sortBy?: 'date' | 'title' | 'readTime';
  sortOrder?: 'asc' | 'desc';
}

export interface BlogListResponse {
  posts: BlogPost[];
  totalCount: number;
  currentPage: number;
  totalPages: number;
  hasNextPage: boolean;
  hasPrevPage: boolean;
}
