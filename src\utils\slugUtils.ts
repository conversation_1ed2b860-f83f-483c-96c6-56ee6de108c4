/**
 * Utility functions for handling slugs and URL normalization
 */

/**
 * Normalizes a string to a proper slug format
 * - Converts to lowercase
 * - Replaces spaces with hyphens
 * - Removes special characters except hyphens
 * - Removes multiple consecutive hyphens
 * - Trims hyphens from start and end
 */
export function normalizeSlug(input: string): string {
  if (!input) return '';
  
  return input
    .toLowerCase()
    .trim()
    .replace(/\s+/g, '-')           // Replace spaces with hyphens
    .replace(/[^a-z0-9-]/g, '')     // Remove special characters except hyphens
    .replace(/-+/g, '-')            // Replace multiple hyphens with single hyphen
    .replace(/^-+|-+$/g, '');       // Remove hyphens from start and end
}

/**
 * Converts a name to a slug format
 * Same as normalizeSlug but more explicit naming for form usage
 */
export function nameToSlug(name: string): string {
  return normalizeSlug(name);
}

/**
 * Validates if a slug is in the correct format
 */
export function isValidSlug(slug: string): boolean {
  if (!slug) return false;
  
  // Check if slug matches the expected pattern
  const slugPattern = /^[a-z0-9]+(-[a-z0-9]+)*$/;
  return slugPattern.test(slug);
}

/**
 * Ensures a slug is properly formatted for URL usage
 * This function should be used before constructing URLs
 */
export function ensureValidSlug(slug: string): string {
  if (!slug) return '';
  
  const normalized = normalizeSlug(slug);
  
  // If the normalized slug is empty, return a default
  if (!normalized) {
    console.warn(`Invalid slug provided: "${slug}". Using default.`);
    return 'invalid-slug';
  }
  
  return normalized;
}

/**
 * Creates a shop URL with properly normalized slugs
 */
export function createShopUrl(mainCategorySlug?: string, categorySlug?: string, productSlug?: string): string {
  const basePath = '/shop';
  
  if (!mainCategorySlug) {
    return basePath;
  }
  
  const normalizedMainCategory = ensureValidSlug(mainCategorySlug);
  
  if (!categorySlug) {
    return `${basePath}/${normalizedMainCategory}`;
  }
  
  const normalizedCategory = ensureValidSlug(categorySlug);
  
  if (!productSlug) {
    return `${basePath}/${normalizedMainCategory}/${normalizedCategory}`;
  }
  
  const normalizedProduct = ensureValidSlug(productSlug);
  return `${basePath}/${normalizedMainCategory}/${normalizedCategory}/${normalizedProduct}`;
}

/**
 * Extracts and normalizes slug from URL parameters
 */
export function normalizeUrlParams(params: { [key: string]: string }): { [key: string]: string } {
  const normalized: { [key: string]: string } = {};
  
  for (const [key, value] of Object.entries(params)) {
    if (key.includes('Slug') || key.includes('slug')) {
      normalized[key] = ensureValidSlug(value);
    } else {
      normalized[key] = value;
    }
  }
  
  return normalized;
}

/**
 * Debug function to log slug transformations
 */
export function debugSlugTransformation(original: string, context: string = ''): string {
  const normalized = normalizeSlug(original);
  
  if (process.env.NODE_ENV === 'development' && original !== normalized) {
    console.log(`Slug transformation ${context}:`, {
      original,
      normalized,
      isValid: isValidSlug(normalized)
    });
  }
  
  return normalized;
}
