import { Metadata } from "next";
import ShopPageClient from "@/components/shop/ShopPageClient";

// Types
interface Product {
  id: number;
  sku: string;
  name: string;
  slug: string;
  imageUrl: string;
  price: number;
  salePrice?: number;
  inStock: boolean;
  created_at: string;
}

interface MainCategory {
  id: number;
  name: string;
  slug: string;
  imageUrl: string;
  count: number;
}

interface ShopPageData {
  products: {
    pagination: {
      total: number;
      page: number;
      limit: number;
    };
    data: Product[];
  };
  mainCategories: MainCategory[];
}

// Server-side data fetching
async function getShopPageData(page: number = 1, limit: number = 20): Promise<ShopPageData | null> {
  try {
    const baseUrl = process.env.NODE_ENV === 'production'
      ? 'https://cocojojo.com'
      : 'http://localhost:3000';

    // Fetch products and main categories in parallel
    const [productsResponse, categoriesResponse] = await Promise.all([
      fetch(`${baseUrl}/api/shop/products?page=${page}&limit=${limit}`, {
        next: { revalidate: 3600 } // 1 hour cache
      }),
      fetch(`${baseUrl}/api/shop/main-categories`, {
        next: { revalidate: 86400 } // 24 hours cache
      })
    ]);

    if (!productsResponse.ok || !categoriesResponse.ok) {
      console.error('Failed to fetch shop data:', {
        products: productsResponse.status,
        categories: categoriesResponse.status
      });
      return null;
    }

    const [products, categories] = await Promise.all([
      productsResponse.json(),
      categoriesResponse.json()
    ]);

    return {
      products,
      mainCategories: categories.data || []
    };
  } catch (error) {
    console.error('Error fetching shop page data:', error);
    return null;
  }
}

// Generate metadata
export const metadata: Metadata = {
  title: 'Shop - Premium Organic Beauty Products | CocoJojo',
  description: 'Discover our complete collection of premium organic beauty products. Shop natural skincare, essential oils, and wellness products with fast shipping.',
  keywords: 'organic beauty, natural skincare, essential oils, premium cosmetics, wellness products',
  openGraph: {
    title: 'Shop - Premium Organic Beauty Products | CocoJojo',
    description: 'Discover our complete collection of premium organic beauty products. Shop natural skincare, essential oils, and wellness products with fast shipping.',
    type: "website",
    url: "/shop",
    images: [
      {
        url: "/images/shop-og.jpg",
        width: 1200,
        height: 630,
        alt: "CocoJojo Shop - Premium Organic Beauty Products",
      },
    ],
  },
  twitter: {
    card: "summary_large_image",
    title: 'Shop - Premium Organic Beauty Products | CocoJojo',
    description: 'Discover our complete collection of premium organic beauty products.',
  },
  alternates: {
    canonical: "/shop",
  },
};

export default async function ShopPage({
  searchParams,
}: {
  searchParams: { page?: string; limit?: string };
}) {
  // Get pagination parameters from URL
  const page = parseInt(searchParams.page || '1');
  const limit = parseInt(searchParams.limit || '20');

  // Fetch data server-side for SEO
  const data = await getShopPageData(page, limit);

  if (!data) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <h1 className="text-2xl font-bold text-gray-800 mb-4">Shop Temporarily Unavailable</h1>
          <p className="text-gray-600">We&apos;re working to restore the shop. Please try again later.</p>
        </div>
      </div>
    );
  }

  return (
    <>
      {/* JSON-LD Structured Data */}
      <script
        type="application/ld+json"
        dangerouslySetInnerHTML={{
          __html: JSON.stringify({
            "@context": "https://schema.org",
            "@type": "CollectionPage",
            "name": "Shop - Premium Organic Beauty Products",
            "description": "Discover our complete collection of premium organic beauty products",
            "url": "https://cocojojo.com/shop",
            "mainEntity": {
              "@type": "ItemList",
              "numberOfItems": data.products.pagination.total,
              "itemListElement": data.products.data.slice(0, 10).map((product: Product, index: number) => ({
                "@type": "Product",
                "position": index + 1,
                "name": product.name,
                "url": `https://cocojojo.com/product/${product.slug}`,
                "image": product.imageUrl,
                "offers": {
                  "@type": "Offer",
                  "price": product.salePrice || product.price,
                  "priceCurrency": "USD",
                  "availability": product.inStock ? "https://schema.org/InStock" : "https://schema.org/OutOfStock"
                }
              }))
            },
            "breadcrumb": {
              "@type": "BreadcrumbList",
              "itemListElement": [
                {
                  "@type": "ListItem",
                  "position": 1,
                  "name": "Home",
                  "item": "https://cocojojo.com"
                },
                {
                  "@type": "ListItem",
                  "position": 2,
                  "name": "Shop",
                  "item": "https://cocojojo.com/shop"
                }
              ]
            }
          })
        }}
      />

      <ShopPageClient
        initialProducts={data.products}
        mainCategories={data.mainCategories}
      />
    </>
  );
}

// Enable ISR
export const revalidate = 3600; // 1 hour
