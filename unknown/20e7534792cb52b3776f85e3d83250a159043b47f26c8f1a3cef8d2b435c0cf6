"use client";

import Image from "next/image";
import Link from "next/link";
import { useRouter } from "next/navigation";
import { FiTrash2, FiM<PERSON>, FiPlus, FiShoppingCart } from "react-icons/fi";
import { useCartStore } from "@/hooks/useCartStore";
import { useEffect } from "react";

const CartModal = () => {
  const router = useRouter();
  const { items, totalPrice, counter, isLoading, removeItem, updateItemQuantity, getCart } = useCartStore();

  // Load cart on component mount
  useEffect(() => {
    getCart();
  }, [getCart]);

  const handleCheckout = () => {
    router.push("/checkout");
  };

  const handleQuantityChange = (itemId: string, currentQuantity: number, change: number) => {
    const newQuantity = currentQuantity + change;
    if (newQuantity > 0) {
      updateItemQuantity(itemId, newQuantity);
    }
  };

  return (
    <div className="w-96 max-w-[90vw] absolute p-4 rounded-md shadow-[0_3px_10px_rgb(0,0,0,0.2)] bg-white top-12 right-0 flex flex-col gap-4 z-20">
      <h3 className="text-lg font-medium border-b pb-2">Your Cart ({counter} items)</h3>

      {items.length === 0 ? (
        <div className="py-8 text-center text-gray-500 flex flex-col items-center">
          <FiShoppingCart size={32} className="mb-2" />
          <p>Your cart is empty</p>
          <Link href="/shop" className="mt-4 text-main-color hover:underline">
            Continue Shopping
          </Link>
        </div>
      ) : (
        <>
          <div className="max-h-80 overflow-y-auto flex flex-col gap-4">
            {items.map((item) => (
              <div key={item.id} className="flex gap-3 border-b pb-3">
                <div className="w-16 h-16 relative flex-shrink-0 bg-gray-100 rounded overflow-hidden">
                  {item.image ? (
                    <Image
                      src={item.image}
                      alt={item.name}
                      fill
                      sizes="64px"
                      className="object-cover"
                    />
                  ) : (
                    <div className="w-full h-full flex items-center justify-center text-gray-400">
                      No image
                    </div>
                  )}
                </div>

                <div className="flex-1 min-w-0">
                  <h4 className="font-medium text-sm truncate">{item.name}</h4>
                  <p className="text-xs text-gray-500 mb-1">SKU: {item.sku}</p>

                  <div className="flex justify-between items-center">
                    <div className="flex items-center gap-1 border rounded-md">
                      <button
                        onClick={() => handleQuantityChange(item.id, item.quantity, -1)}
                        className="p-1 text-gray-500 hover:text-main-color disabled:opacity-50"
                        disabled={item.quantity <= 1 || isLoading}
                      >
                        <FiMinus size={14} />
                      </button>
                      <span className="px-2 text-sm">{item.quantity}</span>
                      <button
                        onClick={() => handleQuantityChange(item.id, item.quantity, 1)}
                        className="p-1 text-gray-500 hover:text-main-color disabled:opacity-50"
                        disabled={item.quantity >= item.stockQuantity || isLoading}
                      >
                        <FiPlus size={14} />
                      </button>
                    </div>

                    <div className="flex items-center gap-2">
                      <span className="font-medium">${(item.price * item.quantity).toFixed(2)}</span>
                      <button
                        onClick={() => removeItem(item.id)}
                        className="text-gray-400 hover:text-red-500 p-1"
                        disabled={isLoading}
                      >
                        <FiTrash2 size={16} />
                      </button>
                    </div>
                  </div>
                </div>
              </div>
            ))}
          </div>

          <div className="border-t pt-3">
            <div className="flex justify-between mb-4">
              <span className="font-medium">Subtotal:</span>
              <span className="font-bold">${totalPrice.toFixed(2)}</span>
            </div>

            <div className="flex gap-2">
              <Link
                href="/cart"
                className="flex-1 py-2 px-4 border border-main-color text-main-color rounded-md text-center hover:bg-main-color/5 transition-colors"
              >
                View Cart
              </Link>
              <button
                onClick={handleCheckout}
                className="flex-1 py-2 px-4 bg-main-color text-white rounded-md hover:bg-main-color/90 transition-colors disabled:opacity-70"
                disabled={isLoading}
              >
                Checkout
              </button>
            </div>
          </div>
        </>
      )}
    </div>
  );
};

export default CartModal;