import { NextRequest, NextResponse } from 'next/server';

// Get the API base URL from environment variables
const API_BASE_URL = process.env.NEXT_PUBLIC_API_BASE_URL || 'http://localhost:3300';
// API path prefix
const API_PATH_PREFIX = process.env.NEXT_PUBLIC_API_PATH_PREFIX || '/api/store';

// Log the API configuration to help with debugging
console.log('Category Products API - Base URL:', API_BASE_URL);
console.log('Category Products API - Path Prefix:', API_PATH_PREFIX);

// GET handler for fetching products by category ID
export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const categoryId = params.id;

    // Try both possible endpoint formats
    const backendUrl = `${API_BASE_URL}${API_PATH_PREFIX}/categories/${categoryId}/products`;
    console.log(`Calling backend URL: ${backendUrl}`);

    const response = await fetch(backendUrl, {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
      },
    });

    if (!response.ok) {
      return NextResponse.json(
        { error: `Failed to fetch products for category ID ${categoryId}` },
        { status: response.status }
      );
    }

    let data;
    try {
      data = await response.json();
    } catch (e) {
      console.error(`Error parsing JSON response for category ${categoryId}:`, e);
      // Return empty array if we can't parse the response
      return NextResponse.json([]);
    }

    console.log(`API response for category ${categoryId} products:`, data);

    // If the response is not an array, convert it to an empty array
    if (!Array.isArray(data)) {
      console.warn(`Expected array but got ${typeof data} for category ${categoryId}`);
      return NextResponse.json([]);
    }

    return NextResponse.json(data);
  } catch (error) {
    console.error(`API proxy error (GET /categories/${params.id}/products):`, error);
    return NextResponse.json(
      { error: `Failed to fetch products for category ID ${params.id}` },
      { status: 500 }
    );
  }
}

// POST handler for creating a new product in a specific category
export async function POST(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const categoryId = params.id;
    const body = await request.json();

    console.log(`API proxy - Creating product for category ${categoryId}:`, body);

    // Always remove the ProductAttribute field for the simple product endpoints
    // Grouped products should use their dedicated endpoints
    if ('ProductAttribute' in body) {
      delete body.ProductAttribute;
      console.log(`API proxy - Removed ProductAttribute field for product creation in category ${categoryId}`);
    }

    // Also remove variants field if present
    if ('variants' in body) {
      delete body.variants;
      console.log(`API proxy - Removed variants field for product creation in category ${categoryId}`);
    }

    const backendUrl = `${API_BASE_URL}${API_PATH_PREFIX}/categories/${categoryId}/products`;
    console.log(`API proxy - Creating product for category ${categoryId} at URL: ${backendUrl}`);
    console.log(`API proxy - Cleaned request body:`, body);

    const response = await fetch(backendUrl, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(body),
    });

    if (!response.ok) {
      const errorText = await response.text();
      console.error(`Error response from backend: ${errorText}`);
      return NextResponse.json(
        { error: `Failed to create product for category ID ${categoryId}` },
        { status: response.status }
      );
    }

    const data = await response.json();
    console.log(`Created product for category ${categoryId}:`, data);

    return NextResponse.json(data, { status: 201 });
  } catch (error) {
    console.error(`API proxy error (POST /categories/${params.id}/products):`, error);
    return NextResponse.json(
      { error: `Failed to create product for category ID ${params.id}` },
      { status: 500 }
    );
  }
}
