"use client";

import { useState } from "react";
import { useRouter } from "next/navigation";
import Image from "next/image";
import Link from "next/link";
import { FiGrid, FiList, FiChevronRight } from "react-icons/fi";
import ProductGrid from "@/components/shop/ProductGrid";
import ProductList from "@/components/shop/ProductList";
import { createShopUrl, ensureValidSlug } from "@/utils/slugUtils";

// Types
interface Category {
  id: number;
  name: string;
  slug: string;
  imageUrl: string;
  count: number;
}

interface Product {
  id: number;
  sku: string;
  name: string;
  slug: string;
  imageUrl: string;
  price: number;
  salePrice: number | null;
  inStock: boolean;
  created_at: string;
  shortDescription?: string;
  tags?: string[];
}

interface MainCategoryData {
  mainCategory?: {
    id: number;
    name: string;
    slug: string;
    imageUrl: string;
  };
  categories: Category[];
  data: Product[];
  pagination?: {
    total: number;
    page: number;
    limit: number;
  };
}

interface MainCategoryPageClientProps {
  initialData: MainCategoryData;
  mainCategorySlug: string;
}

const MainCategoryPageClient = ({ initialData, mainCategorySlug }: MainCategoryPageClientProps) => {
  const [viewMode, setViewMode] = useState<"grid" | "list">("grid");
  const [sortBy, setSortBy] = useState<string>("featured");
  const router = useRouter();

  const { mainCategory, categories, data: products, pagination } = initialData;

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Hero Section */}
      <div 
        className="relative h-64 md:h-80 bg-cover bg-center bg-no-repeat"
        style={{
          backgroundImage: `url('${mainCategory?.imageUrl || 'https://images.unsplash.com/photo-1596462502278-27bfdc403348?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=2340&q=80'}')`
        }}
      >
        <div className="absolute inset-0 bg-black bg-opacity-40"></div>
        <div className="relative z-10 flex items-center justify-center h-full">
          <div className="text-center text-white">
            <h1 className="text-4xl md:text-5xl font-bold mb-4">{mainCategory?.name || 'Category'}</h1>
            <p className="text-lg md:text-xl max-w-2xl mx-auto">
              Discover our premium collection of {mainCategory?.name?.toLowerCase() || 'beauty'} products
            </p>
          </div>
        </div>
      </div>

      {/* Main Content */}
      <div className="px-4 md:px-8 lg:px-16 xl:px-32 2xl:px-64 py-12">
        {/* Breadcrumb Navigation */}
        <nav className="flex items-center text-sm text-gray-600 mb-8">
          <Link href="/" className="hover:text-main-color">
            Home
          </Link>
          <FiChevronRight className="mx-2" />
          <Link href="/shop" className="hover:text-main-color">
            Shop
          </Link>
          <FiChevronRight className="mx-2" />
          <span className="font-medium text-gray-800">{mainCategory?.name}</span>
        </nav>

        {/* Categories Grid */}
        {categories && categories.length > 0 && (
          <section className="mb-16">
            <h2 className="text-2xl font-bold text-gray-800 mb-8">Shop by Category</h2>
            <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
              {categories.map((category) => (
                <Link
                  key={category.id}
                  href={createShopUrl(ensureValidSlug(mainCategorySlug), ensureValidSlug(category.slug))}
                  className="group relative overflow-hidden rounded-lg shadow-md hover:shadow-lg transition-all duration-300"
                >
                  <div className="relative h-48 w-full overflow-hidden">
                    <Image
                      src={category.imageUrl}
                      alt={category.name}
                      fill
                      className="object-cover transition-transform duration-500 group-hover:scale-110"
                    />
                    <div className="absolute inset-0 bg-gradient-to-t from-black/70 to-transparent"></div>
                  </div>
                  <div className="absolute bottom-0 left-0 right-0 p-4 text-white">
                    <h3 className="text-lg font-medium">{category.name}</h3>
                    <p className="text-sm opacity-90">{category.count} products</p>
                  </div>
                </Link>
              ))}
            </div>
          </section>
        )}

        {/* Products Section */}
        {products && products.length > 0 && (
          <section>
            {/* Section Header */}
            <div className="flex flex-col md:flex-row md:items-center md:justify-between mb-8">
              <div>
                <h2 className="text-2xl font-bold text-gray-800 mb-2">
                  All {mainCategory?.name} Products
                </h2>
                <p className="text-gray-600">
                  {pagination?.total || products.length} products available
                </p>
              </div>

              {/* Desktop Controls */}
              <div className="hidden md:flex items-center space-x-4 mt-4 md:mt-0">
                {/* View Mode Toggle */}
                <div className="flex items-center bg-white rounded-lg border p-1">
                  <button
                    onClick={() => setViewMode("grid")}
                    className={`p-2 rounded ${
                      viewMode === "grid"
                        ? "bg-main-color text-white"
                        : "text-gray-600 hover:text-main-color"
                    }`}
                  >
                    <FiGrid size={18} />
                  </button>
                  <button
                    onClick={() => setViewMode("list")}
                    className={`p-2 rounded ${
                      viewMode === "list"
                        ? "bg-main-color text-white"
                        : "text-gray-600 hover:text-main-color"
                    }`}
                  >
                    <FiList size={18} />
                  </button>
                </div>

                {/* Sort Dropdown */}
                <select
                  value={sortBy}
                  onChange={(e) => setSortBy(e.target.value)}
                  className="bg-white border rounded-lg px-3 py-2 text-sm focus:outline-none focus:ring-2 focus:ring-main-color"
                >
                  <option value="featured">Sort by: Featured</option>
                  <option value="newest">Sort by: Newest</option>
                  <option value="price-low">Sort by: Price: Low to High</option>
                  <option value="price-high">Sort by: Price: High to Low</option>
                </select>
              </div>
            </div>

            {/* Mobile Controls */}
            <div className="md:hidden space-y-4 mb-6">
              {/* Mobile View Mode Toggle */}
              <div className="flex items-center justify-between">
                <div className="flex items-center bg-white rounded-lg border p-1">
                  <button
                    onClick={() => setViewMode("grid")}
                    className={`p-2 rounded ${
                      viewMode === "grid"
                        ? "bg-main-color text-white"
                        : "text-gray-600 hover:text-main-color"
                    }`}
                  >
                    <FiGrid size={18} />
                  </button>
                  <button
                    onClick={() => setViewMode("list")}
                    className={`p-2 rounded ${
                      viewMode === "list"
                        ? "bg-main-color text-white"
                        : "text-gray-600 hover:text-main-color"
                    }`}
                  >
                    <FiList size={18} />
                  </button>
                </div>
              </div>

              {/* Mobile Sort */}
              <select
                value={sortBy}
                onChange={(e) => setSortBy(e.target.value)}
                className="w-full bg-white border rounded-lg px-3 py-2 text-sm focus:outline-none focus:ring-2 focus:ring-main-color"
              >
                <option value="featured">Sort by: Featured</option>
                <option value="newest">Sort by: Newest</option>
                <option value="price-low">Sort by: Price: Low to High</option>
                <option value="price-high">Sort by: Price: High to Low</option>
              </select>
            </div>

            {/* Products Display */}
            <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
              {products.map((product) => (
                <Link
                  key={product.id}
                  href={`/product/${ensureValidSlug(product.slug)}`}
                  className="group bg-white rounded-lg shadow-sm hover:shadow-md transition-shadow duration-300"
                >
                  <div className="relative h-64 overflow-hidden rounded-t-lg">
                    <Image
                      src={product.imageUrl}
                      alt={product.name}
                      fill
                      className="object-cover transition-transform duration-500 group-hover:scale-105"
                    />
                    {product.salePrice && (
                      <div className="absolute top-2 left-2 bg-red-500 text-white px-2 py-1 rounded text-xs font-medium">
                        Sale
                      </div>
                    )}
                    {!product.inStock && (
                      <div className="absolute top-2 right-2 bg-gray-500 text-white px-2 py-1 rounded text-xs font-medium">
                        Out of Stock
                      </div>
                    )}
                  </div>
                  <div className="p-4">
                    <h3 className="font-medium text-gray-800 mb-2 line-clamp-2">{product.name}</h3>
                    {product.shortDescription && (
                      <p className="text-sm text-gray-600 mb-3 line-clamp-2">{product.shortDescription}</p>
                    )}
                    <div className="flex items-center justify-between">
                      <div className="flex items-center space-x-2">
                        {product.salePrice ? (
                          <>
                            <span className="text-lg font-bold text-main-color">${product.salePrice}</span>
                            <span className="text-sm text-gray-500 line-through">${product.price}</span>
                          </>
                        ) : (
                          <span className="text-lg font-bold text-gray-800">${product.price}</span>
                        )}
                      </div>
                    </div>
                  </div>
                </Link>
              ))}
            </div>
          </section>
        )}
      </div>
    </div>
  );
};

export default MainCategoryPageClient;
