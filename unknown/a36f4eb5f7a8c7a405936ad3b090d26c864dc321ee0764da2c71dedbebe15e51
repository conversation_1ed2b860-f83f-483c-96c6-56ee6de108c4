import { NextRequest, NextResponse } from 'next/server';
import { ensureValidSlug } from '@/utils/slugUtils';

// Get the API base URL and path prefix from environment variables
const API_BASE_URL = process.env.NEXT_PUBLIC_API_BASE_URL || 'http://localhost:3300';
const API_SHOP_PATH_PREFIX = process.env.NEXT_PUBLIC_API_SHOP_PATH_PREFIX || '/api/shop';

// Log the API configuration to help with debugging
console.log('Shop Category API - Base URL:', API_BASE_URL);
console.log('Shop Category API - Shop Path Prefix:', API_SHOP_PATH_PREFIX);

// GET handler for fetching category products by main category and category slug
export async function GET(
  request: NextRequest,
  { params }: { params: { mainCategorySlug: string; categorySlug: string } }
) {
  try {
    const rawMainSlug = params.mainCategorySlug;
    const rawCategorySlug = params.categorySlug;
    const mainSlug = ensureValidSlug(rawMainSlug);
    const categorySlug = ensureValidSlug(rawCategorySlug);
    const url = new URL(request.url);
    const searchParams = url.searchParams;

    // Get pagination parameters
    const page = searchParams.get('page') || '1';
    const limit = searchParams.get('limit') || '20';

    console.log(`Shop Category API - Raw main slug: ${rawMainSlug}, Normalized: ${mainSlug}`);
    console.log(`Shop Category API - Raw category slug: ${rawCategorySlug}, Normalized: ${categorySlug}`);
    console.log(`Shop Category API - Page: ${page}, Limit: ${limit}`);

    // Construct the backend URL with pagination - matches backend pattern /shop/:mainCategorySlug/:categorySlug
    const queryParams = new URLSearchParams();
    queryParams.append('page', page);
    queryParams.append('limit', limit);

    const backendUrl = `${API_BASE_URL}${API_SHOP_PATH_PREFIX}/${mainSlug}/${categorySlug}?${queryParams.toString()}`;

    console.log(`Shop Category API - Calling backend URL: ${backendUrl}`);

    const response = await fetch(backendUrl, {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
      },
      // Add cache control for ISR
      next: { revalidate: 86400 } // 24 hours cache
    });

    if (!response.ok) {
      console.error(`Backend response error: ${response.status} ${response.statusText}`);
      return NextResponse.json(
        { error: `Failed to fetch products for category ${categorySlug} in ${mainSlug}` },
        { status: response.status }
      );
    }

    const data = await response.json();
    console.log(`Shop Category API - Response received for ${mainSlug}/${categorySlug}:`, {
      totalProducts: data.pagination?.total || data.data?.length || 0,
      categories: data.categories?.length || 0
    });

    return NextResponse.json(data);
  } catch (error) {
    console.error(`Shop Category API error for ${params.mainCategorySlug}/${params.categorySlug}:`, error);
    return NextResponse.json(
      { error: 'Failed to fetch category products' },
      { status: 500 }
    );
  }
}
