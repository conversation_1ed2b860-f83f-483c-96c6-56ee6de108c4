"use client";

import Image from "next/image";
import Link from "next/link";
import { useState } from "react";
import { useAuthStore } from "@/hooks/useAuthStore";
import { useRouter } from "next/navigation";
import { useCartStore } from "@/hooks/useCartStore";

const Menu = () => {
  const [open, setOpen] = useState(false);
  const { isAuthenticated, logout } = useAuthStore();
  const { counter = 1 } = useCartStore();
  const router = useRouter();

  const handleLogout = async () => {
    await logout();
    setOpen(false);
    router.push("/login");
  };

  const handleLinkClick = () => {
    setOpen(false);
  };

  return (
    <div className="">
      <Image
        src="/menu.png"
        alt=""
        width={28}
        height={28}
        className="cursor-pointer"
        onClick={() => setOpen(!open)}
      />
      {open && (
        <div className="absolute bg-black text-white left-0 top-20 w-full h-[calc(100vh-80px)] flex flex-col items-center justify-center gap-8 text-xl z-10">
          <Link href="/" onClick={handleLinkClick}>Homepage</Link>
          <Link href="/shop" onClick={handleLinkClick}>Shop</Link>
          <Link href="/" onClick={handleLinkClick}>Blogs</Link>
          <Link href="/" onClick={handleLinkClick}>About</Link>
          <Link href="/" onClick={handleLinkClick}>Contact</Link>

          {isAuthenticated() ? (
            <>
              <div
                className="cursor-pointer"
                onClick={() => {
                  setOpen(false);
                  router.push("/profile");
                }}
              >
                Profile
              </div>
              <div className="cursor-pointer" onClick={handleLogout}>Logout</div>
            </>
          ) : (
            <Link href="/login" onClick={handleLinkClick}>Login</Link>
          )}

          <Link href="/" onClick={handleLinkClick}>Cart({counter})</Link>
        </div>
      )}
    </div>
  );
};

export default Menu;