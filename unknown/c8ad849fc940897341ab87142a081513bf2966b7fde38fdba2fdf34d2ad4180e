"use client";

import { useEffect, useState } from "react";
import Image from "next/image";
import Link from "next/link";
import { FiShoppingBag, FiHeart, FiEye, FiChevronLeft, FiChevronRight } from "react-icons/fi";
import { useCartStore } from "@/hooks/useCartStore";
import FilterBar from "./FilterBar";

interface Product {
  id: string;
  name: string;
  slug: string;
  price: {
    price: number;
  };
  media: {
    mainMedia: {
      image: {
        url: string;
      };
    };
    items: Array<{
      image: {
        url: string;
      };
    }>;
  };
}

interface ProductListProps {
  categoryId?: string;
  searchParams?: {
    query?: string;
    page?: string;
    limit?: string;
    type?: string;
    min?: string;
    max?: string;
    cat?: string;
  };
}

const ProductList = ({ categoryId, searchParams }: ProductListProps) => {
  const [products, setProducts] = useState<Product[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [addingToCartId, setAddingToCartId] = useState<string | null>(null);
  const [filters, setFilters] = useState({
    priceRange: { min: 0, max: 1000 },
    onSale: false,
    isNew: false,
  });
  const [pagination, setPagination] = useState({
    currentPage: parseInt(searchParams?.page || '1'),
    totalPages: 1,
    totalItems: 0,
    itemsPerPage: parseInt(searchParams?.limit || '20'),
  });

  useEffect(() => {
    const fetchProducts = async () => {
      setIsLoading(true);
      setError(null);
      try {
        let url = '/api/shop/products';
        const params = new URLSearchParams();

        if (searchParams?.query) {
          url = '/api/shop/products/search';
          params.append('query', searchParams.query);
        }

        // Add pagination params
        params.append('page', pagination.currentPage.toString());
        params.append('limit', pagination.itemsPerPage.toString());

        // Add filter params
        params.append('min', filters.priceRange.min.toString());
        params.append('max', filters.priceRange.max.toString());
        if (filters.onSale) params.append('onSale', 'true');
        if (filters.isNew) params.append('isNew', 'true');

        if (searchParams?.cat) params.append('cat', searchParams.cat);
        if (categoryId) params.append('categoryId', categoryId);

        console.log(`ProductList - Fetching products with URL: ${url}?${params.toString()}`);
        const response = await fetch(`${url}?${params.toString()}`);
        if (!response.ok) throw new Error('Failed to fetch products');
        const data = await response.json();
        setProducts(data.data);
        setPagination(prev => ({
          ...prev,
          totalPages: Math.ceil(data.pagination.total / data.pagination.limit),
          totalItems: data.pagination.total,
        }));
      } catch (err) {
        setError(err instanceof Error ? err.message : 'Failed to fetch products');
      } finally {
        setIsLoading(false);
      }
    };

    fetchProducts();
  }, [categoryId, searchParams, filters, pagination.currentPage]);

  const handleFilterChange = (newFilters: typeof filters) => {
    setFilters(newFilters);
    setPagination(prev => ({ ...prev, currentPage: 1 })); // Reset to first page on filter change
  };

  if (isLoading) {
    return (
      <div className="space-y-6">
        <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-6">
          {[...Array(8)].map((_, i) => (
            <div key={i} className="animate-pulse">
              <div className="bg-gray-200 aspect-square rounded-lg mb-4"></div>
              <div className="h-4 bg-gray-200 rounded w-3/4 mb-2"></div>
              <div className="h-4 bg-gray-200 rounded w-1/2"></div>
            </div>
          ))}
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="text-center py-8">
        <p className="text-red-500">{error}</p>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Filter Bar */}
      <FilterBar onFilterChange={handleFilterChange} />

      {/* Products Grid */}
      {products.length === 0 ? (
        <div className="text-center py-8 bg-white rounded-lg shadow-sm border">
          <p className="text-gray-500">No products found</p>
        </div>
      ) : (
        <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-6">
          {products.map((product) => (
            <div
              key={product.id}
              className="group bg-white rounded-lg overflow-hidden shadow-sm border hover:shadow-md transition-all duration-300"
            >
              {/* Product Image */}
              <div className="relative h-64 overflow-hidden">
                <Link href={`/product/${product.slug || product.name.toLowerCase().replace(/\s+/g, '-')}`}>
                  <Image
                    src={product.media.mainMedia.image.url}
                    alt={product.name}
                    fill
                    className="object-cover transition-transform duration-500 group-hover:scale-105"
                  />
                </Link>

                {/* Quick Actions */}
                <div className="absolute top-3 right-3 flex flex-col gap-2">
                  <button className="bg-white text-gray-700 hover:text-main-color p-2 rounded-full shadow-md opacity-0 group-hover:opacity-100 transition-opacity duration-300">
                    <FiHeart size={18} />
                  </button>
                  <button className="bg-white text-gray-700 hover:text-main-color p-2 rounded-full shadow-md opacity-0 group-hover:opacity-100 transition-opacity duration-300">
                    <FiEye size={18} />
                  </button>
                </div>

                {/* Add to Cart Button */}
                <div className="absolute bottom-0 left-0 right-0 p-4 bg-gradient-to-t from-black/60 to-transparent translate-y-full group-hover:translate-y-0 transition-transform duration-300">
                  <button
                    className="w-full bg-main-color text-white py-2 rounded-lg flex items-center justify-center gap-2 hover:bg-main-color/90 transition-colors disabled:bg-gray-400"
                    disabled={addingToCartId === product.id}
                    onClick={(e) => {
                      e.preventDefault();
                      e.stopPropagation();

                      setAddingToCartId(product.id);

                      const { addItem } = useCartStore.getState();
                      const productToAdd = {
                        id: product.id,
                        name: product.name,
                        price: product.price.price,
                        image: product.media.mainMedia.image.url,
                        sku: `PROD-${product.id}`,
                        stockQuantity: 100,
                        stockStatus: 'IN_STOCK',
                        variantId: null
                      };

                      addItem(productToAdd, 1);

                      setTimeout(() => {
                        setAddingToCartId(null);
                      }, 800);
                    }}
                  >
                    <FiShoppingBag size={18} />
                    <span>{addingToCartId === product.id ? 'Adding...' : 'Add to Cart'}</span>
                  </button>
                </div>
              </div>

              {/* Product Info */}
              <div className="p-4">
                <Link href={`/product/${product.slug || product.name.toLowerCase().replace(/\s+/g, '-')}`} className="block">
                  <h3 className="text-lg font-medium text-gray-800 hover:text-main-color transition-colors mb-1 truncate">
                    {product.name}
                  </h3>
                </Link>

                {/* Price */}
                <div className="flex items-center gap-2">
                  <span className="text-lg font-medium text-gray-800">${product.price.price.toFixed(2)}</span>
                </div>
              </div>
            </div>
          ))}
        </div>
      )}

      {/* Pagination */}
      {pagination.totalPages > 1 && (
        <div className="flex justify-center items-center gap-2 mt-8">
          <button
            onClick={() => setPagination(prev => ({ ...prev, currentPage: Math.max(1, prev.currentPage - 1) }))}
            disabled={pagination.currentPage === 1}
            className="p-2 rounded-full bg-white border border-gray-300 hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
          >
            <FiChevronLeft size={20} />
          </button>
          <span className="text-gray-600">
            Page {pagination.currentPage} of {pagination.totalPages}
          </span>
          <button
            onClick={() => setPagination(prev => ({ ...prev, currentPage: Math.min(prev.totalPages, prev.currentPage + 1) }))}
            disabled={pagination.currentPage === pagination.totalPages}
            className="p-2 rounded-full bg-white border border-gray-300 hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
          >
            <FiChevronRight size={20} />
          </button>
        </div>
      )}
    </div>
  );
};

export default ProductList;


