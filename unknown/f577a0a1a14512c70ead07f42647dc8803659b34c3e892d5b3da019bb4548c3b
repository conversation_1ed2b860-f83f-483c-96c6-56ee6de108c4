"use client";

import Image from "next/image";
import { useRouter } from "next/navigation";
import { useState, useEffect, useCallback } from "react";
import { FiSearch, FiX } from "react-icons/fi";
import { useDebounce } from "@/hooks/useDebounce";

const SearchBar = () => {
  const router = useRouter();
  const [searchTerm, setSearchTerm] = useState("");
  const [isLoading, setIsLoading] = useState(false);
  const [showResults, setShowResults] = useState(false);
  const [results, setResults] = useState<any[]>([]);
  const debouncedSearchTerm = useDebounce(searchTerm, 300);

  const handleSearch = async (e: React.FormEvent<HTMLFormElement>) => {
    e.preventDefault();
    if (searchTerm.trim()) {
      router.push(`/list?query=${encodeURIComponent(searchTerm.trim())}`);
      setShowResults(false);
    }
  };

  const fetchSearchResults = useCallback(async (query: string) => {
    if (!query.trim()) {
      setResults([]);
      return;
    }

    setIsLoading(true);
    try {
      const response = await fetch(`/api/shop/products/search?query=${encodeURIComponent(query)}&limit=5`);
      if (!response.ok) throw new Error('Search failed');
      const data = await response.json();
      setResults(data);
    } catch (error) {
      console.error('Search error:', error);
      setResults([]);
    } finally {
      setIsLoading(false);
    }
  }, []);

  useEffect(() => {
    if (debouncedSearchTerm) {
      fetchSearchResults(debouncedSearchTerm);
    } else {
      setResults([]);
    }
  }, [debouncedSearchTerm, fetchSearchResults]);

  return (
    <div className="relative flex-1">
      <form
        className="flex items-center justify-between gap-4 bg-gray-100 p-2 rounded-md"
        onSubmit={handleSearch}
      >
        <input
          type="text"
          value={searchTerm}
          onChange={(e) => {
            setSearchTerm(e.target.value);
            setShowResults(true);
          }}
          placeholder="Search products..."
          className="flex-1 bg-transparent outline-none"
        />
        <button type="submit" className="cursor-pointer">
          <FiSearch className="w-5 h-5 text-gray-500" />
        </button>
      </form>

      {/* Search Results Dropdown */}
      {showResults && (searchTerm || isLoading) && (
        <div className="absolute top-full left-0 right-0 mt-1 bg-white rounded-md shadow-lg z-50 max-h-96 overflow-y-auto">
          {isLoading ? (
            <div className="p-4 text-center text-gray-500">Searching...</div>
          ) : results.length > 0 ? (
            <div>
              {results.map((product) => (
                <div
                  key={product.id}
                  className="p-3 hover:bg-gray-50 cursor-pointer flex items-center gap-3"
                  onClick={() => {
                    router.push(`/product/${product.slug}`);
                    setShowResults(false);
                  }}
                >
                  {product.media?.mainMedia?.image?.url && (
                    <div className="w-12 h-12 relative">
                      <Image
                        src={product.media.mainMedia.image.url}
                        alt={product.name}
                        fill
                        className="object-cover rounded"
                      />
                    </div>
                  )}
                  <div>
                    <h4 className="font-medium text-gray-900">{product.name}</h4>
                    <p className="text-sm text-gray-500">${product.price?.price}</p>
                  </div>
                </div>
              ))}
            </div>
          ) : searchTerm ? (
            <div className="p-4 text-center text-gray-500">No results found</div>
          ) : null}
        </div>
      )}
    </div>
  );
};

export default SearchBar;